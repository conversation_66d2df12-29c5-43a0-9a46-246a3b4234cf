---
cssclasses:
  - editor-full
---
# 提示词简介
* `历史起源/编年史/{YYYY-MM-DD}.md` 按照该路径以及格式**生成或更新**笔记
	* 首先确认今天的日期是否正确，如果不确定，可以通过 fetch 等 MCP 工具获取
* 优先读取该笔记的[[#注意事项]] 
---

# 新闻源
* 主要新闻源
    * https://www.cdrb.com.cn/
    * https://www.chengdu.cn/
    * https://www.cditv.cn/ 
        * 该信息源需要额外输出新闻的发布具体时间
        * 使用 `playwright` 访问网页的 HTML 即可获取准确的发布时间
* 通报信息
    * https://cds.sczwfw.gov.cn/col/col15395/index.html?areaCode=510100000000
        * 政府相关的报告，仅查看是否有最新更新，如果有再按照规则获取
	* https://www.msn.cn/zh-cn/channel/topic/%E8%B5%84%E8%AE%AF/tp-Y_77f04c37-b63e-46b4-a990-e926f7d129ff?ocid=BingNewsLanding&nsq=%e6%88%90%e9%83%bd%e8%ad%a6%e6%96%b9%e9%80%9a%e6%8a%a5

---
# 信息获取规则
* 新闻主要获取最近24小时内的，如果不是24小时内则不要写入
	* 可以通过获取新闻的发布时间来进行判断
	* 优先获取文字类新闻，如果是纯视频或图文类则忽略
* 每条信息都需要标记来源和链接
	* 例如：`* 新闻标题\n    * 链接\n    * 出处` 
	* 按照无序列表，分层级的格式输出
* 遵循完整的新闻要素 (5W1H)，将内容进行总结并且精要输出 (尽量在100字以内)
	* 也就是时间，地点，角色，事件等信息
	* 切勿输出无关的信息
* 首先提取新闻标题，**按照内容进行分类**
* 新闻主要以城市新闻为主，过于宏观的新闻不在获取范围内
	* 通报信息首先确认发生的事件(最近一周内)，然后询问用户是否进一步获取更多信息



---
# 注意事项
* 如果没有足够的信息也不需要强行加入, 不要将过时，假信息或重复信息写入
* 部分网站无法使用 `fetch` MCP，建议使用 `playwright` MCP 访问，获取更准确的信息