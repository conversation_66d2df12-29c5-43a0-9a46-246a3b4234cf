"smart_sources:Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/软件管理器/Powershell.md": {"path":"Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/软件管理器/Powershell.md","embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.03906095,-0.00736028,-0.0157197,-0.03542967,-0.03097798,-0.00060986,-0.05953949,0.00831061,0.05610263,-0.00253434,0.00345205,-0.00860766,-0.00900591,0.03057026,0.04122168,0.03057902,0.00215025,0.07917902,-0.06489855,0.00495862,0.06618789,-0.0414101,-0.02107024,-0.07425083,-0.0100829,0.02486019,0.03535479,-0.00811375,-0.05091126,-0.17748356,-0.01802893,-0.02510414,0.01274257,0.01600895,0.056655,-0.03433906,0.01593213,0.03511621,-0.00223604,0.01317371,0.03006768,0.00469066,-0.0053739,0.05536851,-0.04233471,-0.08345295,-0.03689197,-0.01605742,0.01201131,-0.03983977,-0.04379851,-0.04683235,-0.03005309,-0.00775176,-0.05487235,0.02372068,0.00772824,0.00283446,0.09927331,-0.00768765,-0.01182266,0.01131288,-0.22670902,0.08582302,0.05661917,-0.02998092,0.02457799,-0.03211677,-0.03431019,0.02299763,-0.07602929,-0.01522837,-0.04768382,0.06462889,0.05862491,-0.02144335,-0.04827347,-0.01162327,-0.00922794,-0.00766036,0.00575678,0.04028162,0.00276,-0.00627957,-0.02100666,-0.00174577,-0.02585824,0.00227174,0.05549547,0.01617545,-0.00709424,-0.13250493,0.07694226,0.0156947,0.00119304,0.01383374,0.04016612,0.05629029,-0.05340999,0.05955383,-0.05581167,-0.03304848,-0.03544924,-0.0618709,0.02267181,-0.03852351,0.04999403,-0.03071488,-0.02270501,0.00370605,-0.11106731,-0.01304261,0.0420072,-0.04366421,0.04307396,0.02835313,0.01105849,-0.01636876,-0.00381688,0.00348545,-0.00952885,-0.0259746,0.08406639,-0.00421863,-0.00151861,-0.04371706,0.08093251,0.08294919,0.03386225,0.07934539,0.08276318,-0.0278235,-0.04082903,-0.02088661,0.00235192,-0.00317986,-0.0327031,0.02322493,0.03047518,-0.09281853,-0.01244635,-0.00171457,0.00541001,-0.07819991,-0.00984237,0.08851836,-0.06489789,0.0025881,0.01893395,-0.02874423,0.04668138,0.03675411,-0.01775338,-0.05554199,-0.02379975,0.05819992,0.10647466,0.12295765,0.02348702,-0.03171831,-0.01326537,-0.00329335,-0.06828041,0.114319,0.01916705,-0.0497628,-0.03310484,-0.01697562,0.02759947,-0.02496589,0.02111035,-0.02574428,0.02645904,0.02777121,0.04036491,-0.01512925,-0.04387842,-0.01008677,0.0367052,0.03884383,0.05169131,-0.04689619,-0.06394693,0.03439538,0.05298454,-0.09065778,0.00042821,-0.08069039,0.01846126,-0.00735886,-0.07682753,-0.02597669,-0.0406414,-0.03839006,-0.04623104,-0.05594911,0.05687554,0.02289383,0.01976007,-0.01653639,0.08057795,-0.00964087,-0.06130368,-0.02161179,-0.02552022,-0.01092642,0.00426477,-0.001585,0.07334411,0.01315656,0.01531057,-0.02613194,0.02130898,0.00873431,-0.06236612,0.00247075,-0.00515391,-0.02107401,0.06330239,0.03572053,-0.05245897,0.02000228,-0.09953849,-0.20981845,0.01242084,-0.01390498,-0.03171067,0.01002708,-0.0408479,0.03022767,0.04161437,0.0561626,0.04248537,0.08932263,0.02780154,-0.0728225,-0.03534689,0.05436871,0.02135243,0.00529825,-0.02198478,-0.08352496,0.0306302,0.004056,0.05745443,-0.07256681,-0.03956849,0.03709177,-0.03308716,0.09882528,0.03083211,0.03217383,0.02483921,0.05503675,0.0234476,0.04307228,-0.09453272,-0.02120051,0.01273576,-0.02207163,-0.01145919,-0.00934648,0.01434229,-0.01228798,0.0208655,-0.04151063,-0.06948122,-0.01595287,-0.01894557,0.00197698,0.04271203,-0.00429901,0.03400299,-0.02291738,-0.03269163,0.00110738,0.0172786,-0.02645529,-0.02410179,-0.02730541,-0.05967142,-0.04091483,0.04417066,0.02572806,-0.03146617,0.05238059,0.03051204,0.00061809,-0.00511645,0.01893109,-0.00994055,0.02447668,0.00988888,-0.03925674,0.08301426,-0.00638238,-0.00124082,0.05980388,0.01507673,0.00208114,0.0223019,0.03075098,0.00949177,0.06426158,0.01069845,0.03341208,0.00502826,0.03929915,0.00548802,0.02865444,-0.03640899,0.08454178,-0.08477932,-0.07679962,-0.01874831,-0.04802485,0.03250014,0.06763902,-0.0337255,-0.31603172,0.05278291,0.02148047,0.00165986,0.02579219,0.03926852,0.0022675,-0.01286352,-0.03691125,-0.03696388,-0.09080007,0.09218378,-0.02384449,-0.04746155,-0.05105178,-0.02012726,0.05421133,-0.00916393,0.07678001,-0.01417102,0.02100665,0.07232869,0.21659268,0.01853488,0.03396755,0.00933077,-0.02336212,0.03395937,0.10663855,0.03463108,0.04118907,-0.03111611,0.12016929,-0.05969599,0.05983593,0.009861,-0.0518264,0.03542937,0.03769949,0.01311218,-0.04156737,0.0363864,-0.00546504,-0.03250666,0.1047371,0.0174525,0.03515883,-0.04667918,0.00140086,0.04866079,-0.01307089,0.05489568,-0.02203187,0.00284772,0.02818432,0.02592578,0.0169577,-0.04714517,-0.02003881,-0.01878665,0.02169325,-0.00612087,0.09664897,0.08077285,0.06043421],"last_embed":{"hash":"b052e6a46e0f57b9ce72fd569e00df86685a62b8568f577a5510e090a12520ce","tokens":393}},"Xenova/jina-embeddings-v2-base-zh":{"vec":[-0.0462233,-0.02344364,0.03601196,0.02975422,-0.03853555,0.05179002,0.01808045,-0.01936191,-0.02389547,-0.08769491,-0.00263799,0.03459176,-0.02733344,-0.0659572,0.00605074,0.051423,-0.0516023,-0.01903443,-0.08083147,-0.04393337,-0.00109724,-0.00915499,0.0553507,-0.00115934,0.01498405,0.02573782,0.00743403,-0.03166458,-0.02815903,0.00124795,0.06763262,-0.02008253,-0.00679301,0.00094229,0.02612127,0.01646609,0.02745105,0.03155208,0.02387764,-0.01699768,-0.01961652,0.00627809,-0.02829592,-0.01629882,-0.06248883,0.07302245,0.01093685,0.04036095,-0.01293807,0.03370862,0.00818511,-0.025302,0.06107023,0.00058555,-0.01847906,0.00012696,-0.00623989,0.02786696,-0.08424379,0.00502714,-0.028519,-0.02524343,0.03103439,0.01605967,-0.03714433,0.02709816,-0.0143546,-0.01891341,-0.03924932,-0.00043656,0.05299381,0.01221551,0.08976559,-0.0524066,-0.00531277,-0.00716524,0.0110878,-0.01224591,-0.02385175,-0.00260939,-0.05572258,-0.03678579,0.00907386,0.08926047,-0.00043582,-0.03081335,0.02543527,-0.06393993,-0.03440888,0.00513737,-0.01701746,0.01327631,-0.11335742,-0.04459981,0.01139059,0.05411384,0.04648764,-0.06066965,0.00956044,0.00914234,-0.03971686,0.00227758,-0.1065691,-0.00892974,-0.03618002,-0.05721947,-0.04090554,0.08787636,-0.03735353,-0.00878583,0.04740244,0.03364222,-0.06893686,-0.03885696,-0.03566657,-0.02089658,-0.02962723,0.07122062,-0.03097961,-0.00849373,-0.02067895,0.03674321,0.01079032,0.0124291,0.06100539,-0.01679824,-0.01030529,-0.08557525,0.03698665,-0.01276099,0.00319098,0.0607651,-0.00304238,0.06239089,0.0523176,0.04898227,0.03176166,-0.00974105,-0.01770953,-0.00319514,0.10675193,-0.03200647,0.04404661,-0.0277077,0.00412555,-0.00494382,0.01101221,-0.01484754,-0.00324817,0.05013114,-0.01930018,-0.02130827,0.03389492,-0.04270226,0.00013712,-0.02228341,0.03503373,0.00827921,0.0061371,-0.05274631,0.00387399,0.04522111,-0.01603914,0.00287552,0.00828367,0.00688713,-0.02232389,-0.01323589,0.05567719,-0.04601847,-0.00110748,0.03529431,0.01070153,-0.05119127,0.00428818,0.0096441,0.01375549,0.02005934,0.04434392,0.0082346,-0.01359201,0.02344931,0.03400639,-0.00867325,-0.02736093,0.06593109,-0.00090845,0.10012984,0.08975587,0.04446977,0.01902899,0.03636678,0.04143156,-0.00369444,0.04287684,-0.01235803,-0.02387655,-0.05800297,0.05290486,0.07181033,-0.07862076,-0.04806804,-0.01825313,-0.07940293,0.02742981,0.05103453,0.03529676,-0.03415716,0.00693809,-0.0261882,0.00789504,0.02798506,-0.00252813,-0.01474141,0.00457145,-0.02572361,-0.05265841,-0.01007451,0.06374288,-0.05507369,0.01145644,-0.03915229,0.00559851,0.04395472,-0.03915263,-0.05838151,0.015681,0.03093959,-0.02681515,-0.0223757,-0.00060253,0.03382081,0.03566637,-0.03303619,-0.05234758,0.03533246,0.02813094,0.04407249,0.04057437,-0.04305761,-0.00839035,0.01166082,0.02792674,-0.04856225,-0.01500769,0.02536462,0.00342511,0.02819076,-0.03858448,0.07129735,0.05234599,0.01009914,0.03068034,0.01880014,-0.01659831,0.01546793,-0.00327202,0.06672123,0.02084411,-0.05123934,0.00403002,-0.05984211,-0.06962766,0.0613309,0.02994087,-0.04329434,-0.03655006,0.05806902,0.05169844,0.01022957,-0.00914887,-0.00726975,0.00624538,0.01370374,-0.04959084,0.00119726,0.07411009,-0.03464311,0.04457339,0.01641751,-0.00999507,-0.04807617,-0.01637734,0.00678377,0.01201491,0.0328629,0.01653422,-0.02008859,0.07619923,0.06857024,-0.01252263,-0.03979398,-0.0362176,-0.0365543,-0.04851313,-0.00826828,0.01264214,0.01097855,0.00046141,-0.0453662,0.04654053,-0.02607526,-0.05594069,0.01172708,-0.01795145,0.03996135,0.00343543,0.00735585,0.023607,-0.06730923,0.00185367,0.01506677,0.02933706,-0.0518737,-0.03326796,0.06408735,0.00010775,0.01535734,-0.00997953,-0.05579335,-0.05011293,0.04705212,-0.02418217,0.0632529,0.004807,-0.03428956,0.01013766,0.01034908,0.04875112,0.04014981,0.00455415,0.01518953,-0.04870272,-0.08160298,0.00629627,-0.00147235,0.02943056,0.00085031,0.01681659,0.03479184,0.00727785,0.01239435,-0.04504522,0.00818801,0.0182236,0.08736426,-0.00195671,-0.01053397,-0.03939061,0.02103615,0.0298124,-0.02220679,-0.04956385,0.02599541,-0.01231403,0.02549024,-0.00254557,0.02830869,-0.01770745,-0.01436133,-0.08040109,-0.02263248,-0.00325266,0.03608934,0.08430037,-0.03396175,0.02831095,0.01927274,-0.0760969,-0.03783887,-0.01805946,-0.02051482,0.02149048,0.0424908,-0.0335931,-0.00570206,0.00960402,-0.02715268,-0.01417197,-0.03630145,-0.07189945,-0.01068651,-0.02146385,-0.0030132,-0.0306115,0.01856547,-0.05599178,-0.00051023,-0.00517403,-0.00031166,0.04324738,-0.00091527,-0.01309418,-0.04349332,0.06748814,0.02984616,-0.00748277,-0.05563071,0.01205709,-0.05338294,-0.01180428,-0.04551513,-0.05711396,-0.03138139,0.00100885,-0.03674781,-0.05152178,0.02382115,0.00577638,-0.02778213,-0.04145773,0.02069237,0.04060472,0.09369967,-0.05241977,0.02034592,0.01582893,-0.07811794,-0.04875532,0.01775705,0.00896204,-0.04203217,-0.0205367,-0.00610877,-0.07447952,-0.00447176,0.01769652,0.00124661,0.03245739,0.03330415,-0.04490249,-0.0837066,-0.02869472,0.01319884,-0.01977721,0.00637243,0.00995417,-0.04638042,0.00619728,-0.0412889,-0.02430841,0.02736719,-0.00610622,-0.02779355,-0.01620719,0.02856778,0.01024983,-0.0728607,-0.00313217,-0.0102538,-0.01368918,0.01179803,-0.03955169,-0.0557969,0.0300373,0.00458226,-0.01587524,0.0074329,0.02814116,0.02734578,-0.01068927,-0.01254386,-0.02933617,-0.01571866,0.0048343,0.0295192,0.04861349,0.02425288,-0.05591314,0.01699479,0.05442599,-0.03920412,0.03021011,-0.08649381,-0.01508925,0.00717975,0.01315749,-0.00642593,-0.01850482,0.02403298,0.00038264,-0.0439107,-0.02099042,-0.01450917,-0.02208865,0.01489411,-0.00954878,0.02213921,0.018828,-0.03697703,0.00899102,0.02602678,0.0239398,0.03878422,-0.04994747,-0.04457376,-0.04458633,-0.001099,-0.01211541,0.01651555,0.01458049,0.03295834,0.05535305,0.09558187,-0.02876021,0.02473339,0.00558956,-0.01810958,0.01362875,-0.01694408,-0.01085354,-0.01450475,0.09835839,0.00495519,-0.04953573,0.00185876,0.00100878,0.01929541,0.0195311,-0.0320221,0.02251992,0.0639874,-0.00599446,0.01437528,0.00207893,-0.02045386,-0.0090741,0.03330286,0.01421737,0.00972413,0.01017102,0.02632418,0.00633434,-0.06422357,-0.00043971,0.01654145,0.01639142,-0.04077725,-0.00888875,0.00811879,-0.05243327,0.05265041,0.00263938,0.02183796,-0.01622161,0.06006761,-0.01519971,0.00340466,-0.01620105,-0.01681531,0.01003119,0.01202814,-0.02579924,0.00076673,0.05121831,0.03917054,0.00812186,-0.05496028,0.00362785,0.00886431,-0.0196869,0.03454256,0.02730414,-0.01362113,-0.0004839,-0.03243915,0.03833065,-0.04829328,0.02154141,-0.01930417,-0.02977075,-0.00227541,0.03455544,-0.00284881,-0.02734025,0.01596264,-0.08365693,0.05518698,0.05359305,-0.02372401,-0.00817902,-0.0324179,0.03616475,-0.02315564,0.01233937,-0.00161007,0.01426736,0.04642434,0.01375635,0.01892707,-0.00158607,-0.03224469,-0.02063129,0.02549521,0.08786323,-0.03196165,-0.05434465,0.01758378,0.0045912,-0.02339578,-0.01005282,0.04265252,0.03562998,-0.04968836,-0.0235219,-0.03373653,-0.00902893,-0.02602961,0.00653662,-0.05970012,0.02131114,0.03796284,0.02396744,-0.03798785,-0.00301345,0.01870818,-0.0895652,-0.0225387,-0.00510213,0.07713165,0.00365787,0.03464051,0.01896351,0.00882055,0.04499507,0.05288957,0.0210395,0.03230748,-0.00936236,0.00787595,0.03080702,-0.02013073,0.02384152,0.05174675,-0.0185587,0.00061845,-0.08218265,0.0327995,0.01536171,-0.00929302,0.02965291,-0.03963016,-0.03439198,0.05107952,0.03493161,-0.04359963,-0.05299625,-0.0365428,-0.03277323,-0.04316487,0.04890797,-0.0061231,0.01387155,-0.03497647,0.0058855,-0.01033874,-0.05003775,0.01108171,-0.02450769,-0.08145075,-0.04648667,-0.02814452,-0.02542421,0.02438415,0.04414027,-0.05459373,0.04101127,-0.00284619,-0.05776976,0.0246818,-0.02025168,-0.02382218,-0.01209898,0.01120001,-0.0184238,0.02380077,-0.02057998,0.00338481,-0.01015327,-0.07530049,0.00637944,-0.03029389,0.03635069,0.0084508,0.0369613,0.00685522,0.03857859,-0.06511568,0.03563783,-0.05206428,-0.03098044,-0.0473591,-0.00667283,-0.02440425,-0.02211127,-0.01251514,0.00773565,0.01716113,-0.02084854,-0.01027074,0.00791502,-0.02539482,-0.01928503,0.00653369,0.06326154,0.02410082,-0.00464811,0.01267386,-0.02445105,0.05194762,-0.05154487,0.0696478,0.04894178,-0.04785072,0.04044757,0.00718615,0.00365274,0.01525705,-0.01049574,0.04739937,-0.01351852,0.03123724,0.00401435,0.06200679,-0.00221805,-0.01359456,-0.03149905,0.01742053,0.0209644,0.00811402,0.01414317,-0.05402755,0.01829154,0.02845719,0.04306484,0.00589791,-0.02730802,-0.0292708,0.04055259,0.06600253,-0.03021361,-0.04161208,-0.00478002,-0.02596358,-0.0769573,-0.04685478,0.00691848,0.00950237,-0.00528031,0.06314019,-0.01573955,0.00956687,-0.00984503,0.04670051,-0.02132389,-0.03676693,0.02165639,-0.00388435,9.8e-7,-0.01045828,0.05043177,0.02068589,-0.01037451,-0.05392688,-0.04372225,0.00485001,0.03114565,0.00764018],"last_embed":{"tokens":1583,"hash":"103kd6v"}}},"last_read":{"hash":"103kd6v","at":1750658685964},"class_name":"SmartSource","outlinks":[{"title":"windows系统","target":"windows系统","line":38},{"title":".NET","target":"NET Framework","line":47},{"title":".NET","target":"NET Framework","line":47},{"title":"终端","target":"终端","line":53},{"title":"Neovim","target":"Neovim","line":54},{"title":"SSH协议","target":"SSH协议","line":54},{"title":"windows系统","target":"windows系统","line":55},{"title":"cmdlet","target":"cmdlet","line":57},{"title":"powershell的历史","target":"powershell的历史","line":58},{"title":"内存","target":"内存","line":69},{"title":"Windows的执行策略","target":"Windows的执行策略","line":86},{"title":"windows系统","target":"windows系统","line":108},{"title":"425","target":"Pasted image 20240410193444.png","line":110},{"title":"如何新建ps1任务","target":"https://blog.csdn.net/wyounger/article/details/77718374","line":113}],"metadata":{"tags":["操作系统/windows/终端"],"aliases":["Windows PowerShell"],"cssclasses":["editor-full"]},"blocks":{"#---frontmatter---":[1,8],"#":[10,34],"#简介":[35,44],"#简介#{1}":[36,36],"#简介#{2}":[37,37],"#简介#{3}":[38,40],"#简介#{4}":[41,42],"#简介#{5}":[43,44],"#技术实现":[45,64],"#技术实现#建立在NET Framework框架之上":[46,64],"#技术实现#建立在NET Framework框架之上#{1}":[47,51],"#技术实现#建立在NET Framework框架之上#{2}":[52,55],"#技术实现#建立在NET Framework框架之上#{3}":[56,56],"#技术实现#建立在NET Framework框架之上#{4}":[57,57],"#技术实现#建立在NET Framework框架之上#{5}":[58,58],"#技术实现#建立在NET Framework框架之上#{6}":[59,59],"#技术实现#建立在NET Framework框架之上#{7}":[60,60],"#技术实现#建立在NET Framework框架之上#{8}":[61,62],"#技术实现#建立在NET Framework框架之上#{9}":[63,64],"#PowerShell优点":[65,79],"#PowerShell优点#{1}":[67,68],"#PowerShell优点#{2}":[69,70],"#PowerShell优点#{3}":[71,72],"#PowerShell优点#{4}":[73,74],"#PowerShell优点#{5}":[75,76],"#PowerShell优点#{6}":[77,78],"#PowerShell优点#{7}":[79,79],"#常用操作":[80,98],"#常用操作#{1}":[82,85],"#常用操作#{2}":[86,86],"#常用操作#{3}":[87,91],"#常用操作#获取当前在线的用户":[92,98],"#常用操作#获取当前在线的用户#{1}":[93,95],"#常用操作#获取当前在线的用户#{2}":[96,98],"#自动任务编写":[99,209],"#自动任务编写#{1}":[101,104],"#自动任务编写#设置自动任务":[105,168],"#自动任务编写#设置自动任务#Task Scheduler":[107,118],"#自动任务编写#设置自动任务#Task Scheduler#{1}":[108,108],"#自动任务编写#设置自动任务#Task Scheduler#{2}":[109,109],"#自动任务编写#设置自动任务#Task Scheduler#{3}":[110,110],"#自动任务编写#设置自动任务#Task Scheduler#{4}":[111,113],"#自动任务编写#设置自动任务#Task Scheduler#{5}":[114,117],"#自动任务编写#设置自动任务#Task Scheduler#{6}":[118,118],"#自动任务编写#设置自动任务#Powershell 自动任务":[119,168],"#自动任务编写#设置自动任务#Powershell 自动任务#{1}":[120,122],"#自动任务编写#设置自动任务#Powershell 自动任务#{2}":[123,123],"#自动任务编写#设置自动任务#Powershell 自动任务#{3}":[124,127],"#自动任务编写#设置自动任务#Powershell 自动任务#{4}":[128,128],"#自动任务编写#设置自动任务#Powershell 自动任务#{5}":[129,129],"#自动任务编写#设置自动任务#Powershell 自动任务#{6}":[130,133],"#自动任务编写#设置自动任务#Powershell 自动任务#{7}":[134,134],"#自动任务编写#设置自动任务#Powershell 自动任务#{8}":[135,135],"#自动任务编写#设置自动任务#Powershell 自动任务#{9}":[136,139],"#自动任务编写#设置自动任务#Powershell 自动任务#{10}":[140,140],"#自动任务编写#设置自动任务#Powershell 自动任务#{11}":[141,141],"#自动任务编写#设置自动任务#Powershell 自动任务#{12}":[142,145],"#自动任务编写#设置自动任务#Powershell 自动任务#{13}":[146,146],"#自动任务编写#设置自动任务#Powershell 自动任务#{14}":[147,147],"#自动任务编写#设置自动任务#Powershell 自动任务#{15}":[148,157],"#自动任务编写#设置自动任务#Powershell 自动任务#{16}":[158,158],"#自动任务编写#设置自动任务#Powershell 自动任务#{17}":[159,163],"#自动任务编写#设置自动任务#Powershell 自动任务#{18}":[164,165],"#自动任务编写#设置自动任务#Powershell 自动任务#{19}":[166,168],"#自动任务编写#任务查看":[169,209],"#自动任务编写#任务查看#{1}":[171,209]},"last_import":{"mtime":1747536239370,"size":6702,"at":1748488128956,"hash":"103kd6v"},"key":"Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/软件管理器/Powershell.md","last_embed":{"hash":"103kd6v","at":1750658685964}},
"smart_blocks:Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/软件管理器/Powershell.md#自动任务编写": {"path":null,"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":[-0.0363441,-0.02023435,-0.00981397,0.00168206,-0.03883041,0.04013461,0.01170061,-0.04417521,-0.03002347,-0.09721057,-0.03033628,0.02688826,-0.02395987,-0.02191984,-0.03277364,0.05998455,-0.01696044,0.0514283,-0.04129829,-0.07623146,-0.05008178,-0.00437817,0.04665389,-0.04535371,-0.00444056,0.0274472,0.0149166,-0.01986173,-0.0286742,0.02266438,0.07829449,-0.05606657,-0.06664253,-0.02188702,0.02942873,0.02684029,-0.01955019,0.02242451,0.06868437,-0.02272486,0.00156095,-0.01766772,-0.01170419,0.00068653,-0.08488061,0.06971774,-0.03897163,0.06012668,0.01630297,-0.0006115,-0.02585676,0.04496596,0.02311018,0.00238111,0.0029334,-0.03061865,0.00350688,-0.00478168,-0.02763445,0.01130905,-0.03877776,-0.06587308,0.03047522,0.02112319,0.0380553,0.00400969,-0.06416743,-0.03860084,-0.02417029,-0.02856191,-0.02065652,-0.04133086,0.0475809,-0.05511013,0.03915941,0.03234462,0.02447819,0.00377893,0.00161855,0.02187295,-0.06691322,-0.01617548,-0.03657394,0.06845107,-0.01022948,-0.02340395,-0.00517033,-0.06615715,-0.02946839,-0.02340317,-0.00402187,0.03090405,-0.08230645,-0.01709975,0.03547402,0.02464098,0.04962017,-0.04944217,-0.02623286,-0.02822874,-0.05967751,-0.0352132,-0.1189325,0.01920825,-0.04070403,-0.05618553,0.02951771,0.06586476,-0.02542002,-0.0231611,0.0285099,0.05097507,-0.06852195,-0.0306368,-0.00659476,-0.06052455,-0.0311699,0.06281742,-0.03859521,0.04459075,0.06966024,-0.01639917,0.04086018,0.01401252,0.05962792,0.00656505,0.00735663,-0.0680849,0.06029491,-0.00673788,0.05072099,0.02495143,-0.01506942,0.05649928,0.01541892,0.00986583,0.05370691,-0.05233756,0.04025175,0.01983603,0.03315162,-0.0326313,-0.02094487,-0.04401792,0.01653115,0.00657883,-0.01514436,-0.014458,0.00541306,0.03315813,-0.00462237,0.02053083,0.00903171,-0.03724505,-0.03980707,0.01648888,0.0518992,0.01815527,0.03112778,-0.05367097,0.00931845,0.01293519,-0.02542956,0.01162016,-0.02308797,-0.02241566,0.01220662,-0.00142683,0.0282366,-0.01799775,-0.01506777,0.02846754,-0.04055867,-0.04118008,0.00176378,0.00146004,0.01436634,0.03676993,0.02664389,0.00167951,-0.02511165,-0.02644518,-0.00338694,0.00285061,-0.01544044,0.08233816,0.01455422,0.09273963,0.07586252,0.03854641,0.02406589,0.01867035,0.03010171,-0.01497912,0.01978284,0.04966725,0.00994239,-0.00814473,0.02975294,0.07475265,-0.04768629,-0.01839755,0.03401683,-0.02968409,0.02699882,0.04117491,-0.00314581,-0.05201649,0.01932164,-0.00709558,0.00009464,0.03499195,0.09508917,0.00643027,-0.01286792,-0.00735023,-0.06125331,0.00556467,0.03158949,-0.0835246,0.05750085,-0.04210333,-0.00076598,0.00592809,-0.04771855,-0.03316228,-0.01054958,-0.02417625,-0.02549974,-0.02353114,0.0075562,0.07399732,0.06931995,-0.04079853,-0.02536193,-0.00364111,0.0666927,0.05295673,0.02262619,-0.01020999,-0.00094086,-0.01603457,0.02844429,-0.04448058,-0.0179109,0.00081935,-0.01552569,0.01989686,-0.05553401,0.01137794,0.01914748,0.0084731,0.01452098,0.01404711,-0.03305606,-0.02914936,-0.03330684,0.05529113,-0.02147863,-0.04563944,0.00567241,-0.00619549,-0.07117029,0.06688146,0.01608722,-0.00346874,-0.03828841,-0.00003357,0.01249908,0.04776806,-0.03668749,-0.01542577,0.00750281,0.03423822,-0.02952837,-0.01645604,0.07985578,-0.03132491,0.03521819,-0.01985327,0.03128203,0.00647681,-0.02540964,0.02375966,0.06675174,0.02986203,0.02985445,-0.00236015,0.01751816,0.04497307,-0.04623207,-0.00318728,-0.00916557,-0.0537141,-0.04808668,-0.01953508,-0.08303241,-0.00166634,-0.00096066,-0.03427441,0.04985478,-0.04364405,-0.03577447,-0.01727262,0.04117606,0.02074275,0.00248137,0.07022247,-0.02157264,-0.03167951,0.01546506,-0.04991363,0.01176198,0.00637549,-0.03690219,0.07624801,0.02706155,0.01054644,0.01010875,-0.05672864,-0.02330275,0.04549944,-0.04619041,0.02234324,0.00633786,-0.07532684,-0.01741407,-0.02725252,0.02186625,0.04929897,-0.01986818,-0.0499314,-0.02359382,-0.04252366,-0.00626822,-0.01885409,-0.01824243,-0.06122371,0.00830246,0.05484113,0.08516995,-0.03722921,-0.02048407,0.04581446,0.00647229,0.03744015,0.01003164,-0.02421604,0.01703704,0.02976921,0.06419811,0.00851575,-0.06826469,0.07068454,-0.00756586,-0.0175166,-0.01361748,-0.02015011,-0.05272094,-0.03200083,-0.06654491,-0.00525052,0.00958351,-0.03325905,0.07507022,-0.00182282,0.00931252,0.05004593,-0.00778409,-0.06806812,-0.02191987,-0.02978683,-0.01700906,-0.02851844,-0.00123679,-0.00711897,0.00510163,0.00399068,0.02456081,-0.0232487,-0.09025524,0.03864749,-0.00086234,-0.01073637,-0.02462962,0.03195895,0.00166924,-0.01515032,-0.00642881,0.0109946,0.00801515,0.04351171,0.02366449,-0.02417994,0.02665954,0.00340091,-0.00350546,-0.00579213,-0.04997561,-0.04045435,0.00316495,-0.02116982,-0.05608194,0.02373721,-0.02124457,-0.01520008,-0.02694734,-0.0243194,-0.02467151,-0.0425867,-0.01556201,-0.07456234,0.08293384,0.08396401,-0.04988922,0.06306198,0.0271618,-0.08676156,0.00083016,0.01400607,-0.00109432,-0.05896305,0.02657771,-0.00696199,-0.04988351,-0.01050938,0.0365788,0.00278854,0.03887602,0.05249267,-0.04264459,-0.03499799,-0.0195666,0.01275584,0.00240264,0.03778668,0.02054256,-0.02897781,0.03008447,-0.06406471,0.01309273,0.05243754,-0.02850857,0.0119825,0.00976708,0.01241078,0.01104398,-0.04678304,-0.00687513,-0.00032586,0.01213621,0.00198907,-0.07517532,0.00497376,0.03955115,0.04885099,0.00032079,0.02036725,0.03023341,0.0014547,-0.03033759,0.03594379,-0.03257203,0.00919607,0.01881055,0.03288292,0.03343175,0.0218507,0.01447661,0.00697607,0.03886272,0.02628294,0.02049585,-0.03077393,0.02382997,0.06573404,0.02384348,-0.02038038,-0.04759466,0.01082105,0.00926634,0.03487462,0.02413364,-0.02318962,-0.00818375,0.03822546,-0.02423319,0.01307739,0.02098505,0.01015115,0.03209513,0.0306142,-0.01171539,0.05630342,-0.02368307,-0.03799401,-0.00111544,0.02697458,-0.00483636,0.03895002,-0.04651047,0.01015102,0.01040442,0.07567619,-0.01847238,-0.01170692,0.02274545,-0.01727366,-0.00529104,-0.01654127,0.00627668,-0.05860069,0.08983243,0.03776142,0.01069649,-0.07294489,0.01790795,-0.02078643,-0.01844439,0.01409351,0.00214038,0.06043763,-0.02091876,-0.02177804,-0.00693161,0.00702576,-0.00513663,0.03460876,0.02939079,-0.01044449,0.0208503,0.01993388,-0.01736274,-0.0321649,0.02293065,0.00123376,-0.02638362,-0.01983537,0.02265762,0.0662118,0.01573332,0.03024658,-0.01891249,-0.0030344,0.02771257,0.04431828,-0.01574679,-0.00510387,-0.01681894,-0.04190136,0.00793614,0.03034385,0.00222331,-0.04928606,-0.00233684,0.01760538,0.03442323,-0.02365736,0.03693315,-0.00340607,-0.03524543,0.07166065,0.01096856,-0.04356316,-0.03777373,-0.05589512,-0.01386266,0.0211397,0.07084157,-0.01366976,0.00493243,-0.01277019,0.03723445,0.06815588,-0.02523155,-0.03268394,-0.04227173,0.07012391,0.0479943,0.02409085,-0.00040835,-0.02931198,0.01825503,-0.00789688,-0.01407323,0.03855736,-0.01040446,0.01687076,-0.03097926,0.03840043,-0.0144549,-0.03479344,-0.04562223,0.06056083,0.03384469,-0.02796463,0.02298926,-0.00572562,0.0110196,0.00539542,-0.00586646,0.02079917,0.01379327,-0.03292221,0.01191605,-0.05230712,0.00764535,-0.04217688,0.0330934,-0.01244368,0.02375989,0.0458321,0.02089431,-0.00701719,-0.02444207,-0.01890674,-0.11169671,-0.07289039,0.00371453,0.07894696,-0.0099959,-0.03911746,0.01593689,-0.00419642,0.01216763,-0.0018383,0.00936109,0.02900127,-0.01322252,-0.0293324,0.05547826,-0.04201958,0.02204181,0.02697367,-0.02633224,0.00969831,0.00611453,-0.02587141,0.00822505,0.03945972,0.00908618,-0.03088104,-0.05008459,0.06804606,0.03665734,-0.06161531,-0.02065561,0.00224882,-0.02084805,0.00420951,0.03468647,-0.05522137,0.00473164,-0.0041495,0.00832585,-0.02357211,-0.03394634,0.01475868,-0.01917403,-0.04138708,-0.05999501,-0.0361945,-0.05322282,0.01512135,0.0403473,-0.08054458,0.04911098,0.01358443,-0.01872342,0.00974207,0.02472357,-0.00266458,-0.02166541,-0.03678208,0.00190596,-0.00844834,0.02060366,0.01203757,0.01245353,-0.09456064,0.00501461,-0.03448991,-0.01730234,-0.01014815,0.02301616,-0.01379206,0.03653204,-0.00630711,0.00073119,-0.04220004,0.00605644,-0.01934586,-0.02227533,-0.06755975,-0.02811688,-0.06241401,0.00216252,-0.02509906,0.01009001,-0.02476828,-0.0436728,-0.04087571,0.00461042,0.02942713,0.0066977,0.01020991,0.00003163,0.03871121,-0.06692044,0.04734806,-0.07390367,0.01704007,0.07540418,-0.0330929,-0.01211105,-0.01646084,0.01613338,0.06149742,-0.04140377,0.0231322,0.0216725,0.0052083,0.06218257,0.03846319,-0.02460715,-0.03340533,0.00059351,-0.01898627,0.01305662,0.02921009,0.01907127,-0.04759907,-0.02876085,0.05855076,0.04657259,0.0104514,0.04394769,-0.02526548,0.02072591,0.05575353,-0.07871177,-0.03077549,0.00696412,-0.00069208,-0.02865693,-0.03006121,-0.00081403,0.01424499,-0.02003192,0.04208256,0.00726007,-0.02614413,-0.00270158,0.01129787,-0.03555357,-0.07419718,0.01447499,0.02043773,9.2e-7,-0.00568279,0.09085917,0.0357478,0.00760991,0.02967013,-0.0345131,0.00124973,0.02687192,0.01882801],"last_embed":{"hash":"1vs1aic","tokens":876}}},"text":null,"length":0,"last_read":{"hash":"1vs1aic","at":1749002758062},"key":"Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/软件管理器/Powershell.md#自动任务编写","lines":[99,209],"size":2353,"outlinks":[{"title":"windows系统","target":"windows系统","line":10},{"title":"425","target":"Pasted image 20240410193444.png","line":12},{"title":"如何新建ps1任务","target":"https://blog.csdn.net/wyounger/article/details/77718374","line":15}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/软件管理器/Powershell.md#自动任务编写#设置自动任务": {"path":null,"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":[0.00221835,-0.03207164,-0.00273724,-0.00472707,-0.0225195,0.02293068,0.02222821,-0.01008563,-0.01109249,-0.09362099,-0.0073698,0.01788082,-0.03918126,-0.02711193,0.00071591,0.05998696,-0.07072607,0.03921141,-0.06693755,-0.07498843,-0.03337041,-0.00791548,0.03238579,-0.0126559,0.0033335,0.03156885,0.04165398,-0.01868252,-0.03251307,0.00364452,0.08479012,-0.01973651,-0.04781827,0.00613133,-0.00428176,0.0058043,0.00274452,0.01165632,0.07123216,-0.00948668,-0.00991322,-0.01291905,-0.00608703,0.00542699,-0.06722333,0.05881196,-0.05911363,0.04655503,0.00896186,-0.00119571,-0.00386555,0.00615945,0.02580123,-0.01720606,-0.0024737,-0.01698096,-0.01045995,0.04973979,-0.06291837,-0.01367559,-0.02748869,-0.07170293,0.04793863,0.03749982,0.00646822,0.01561368,-0.04875202,-0.05555665,-0.02016782,-0.03165673,0.0125053,-0.02245225,0.04904445,-0.06013822,0.00661688,0.03531291,0.01335308,0.00471151,-0.00301749,0.04541338,-0.08151948,-0.04302118,-0.02820546,0.06198117,-0.00600295,-0.00035404,0.03241631,-0.0421689,-0.03502482,-0.0062442,-0.00891731,-0.01146153,-0.10126347,-0.04339855,0.00383467,0.02531541,0.04763962,-0.0463387,-0.03803653,-0.04803199,-0.08527517,-0.01417296,-0.06918352,0.02613005,-0.04456688,-0.0577825,-0.03379165,0.02720419,-0.04173708,-0.02454004,0.02777628,0.04399225,-0.04170594,-0.00612397,0.01831752,-0.04792031,-0.01901467,0.08835043,-0.04467821,0.02698356,0.04071758,-0.01487691,0.00275293,-0.01546142,0.05687121,0.00151878,0.00642631,-0.06394997,0.04418441,-0.01933266,0.01692398,0.01974464,-0.00940983,0.07141712,-0.01391107,0.00951068,0.07373032,-0.01156009,0.01608566,0.01585599,0.05885505,-0.0234534,-0.0138448,-0.03381643,0.04426266,0.00933996,0.01211851,0.00330549,-0.00081279,0.0010139,0.00313868,0.04825546,0.01377052,-0.03141181,-0.06073789,0.01815114,0.05233992,0.00760841,0.01323621,-0.06084483,-0.00877757,0.00444098,-0.0060226,0.00568973,-0.01595976,0.00303475,-0.00217512,-0.00118829,0.00467834,-0.03048098,-0.03880941,0.050311,-0.04685613,-0.04295739,-0.01882056,0.02477332,0.01219347,0.03439285,0.04176208,-0.00673784,0.01193627,-0.01052778,0.01380615,-0.01723214,-0.03030371,0.08604756,0.01324294,0.08577176,0.06031798,0.04530752,0.02235264,0.03136839,0.02415999,-0.00489974,0.04993023,0.00399727,-0.00100592,-0.01197357,0.03106303,0.04322745,-0.05866599,-0.04040119,0.03651416,-0.03253058,0.05546139,0.05294392,0.00383882,-0.03317011,0.00081661,-0.04619252,0.00280422,0.05989399,0.07587019,0.00144924,-0.00282278,0.00333191,-0.02351587,-0.0348097,0.07285437,-0.09155162,0.04988109,-0.02362784,-0.01253003,-0.01031835,-0.03349602,-0.0491429,0.01404973,0.00698681,0.00254715,-0.02668297,0.03034835,0.0869707,0.02112651,-0.04366136,-0.01446845,0.04115964,0.06612534,0.0386311,0.021897,-0.00723348,0.00219239,-0.03636339,-0.01388971,-0.01637577,-0.00825308,-0.02086345,-0.00939083,0.03595067,-0.03360879,0.04224395,0.01145602,-0.03142306,0.01634299,-0.00094546,-0.01793468,-0.04172975,0.00303271,0.08503567,-0.01649845,-0.05830385,0.00305571,-0.03429122,-0.05812529,0.06874169,0.02119459,-0.02098434,-0.04738133,0.01351903,0.05003076,0.02968219,-0.00849972,-0.02076209,-0.01369317,0.02671336,-0.02903255,-0.00907116,0.090762,-0.03535297,0.02297431,0.00283098,0.01498782,-0.0150231,-0.00911588,0.02507056,0.07854611,0.0250176,0.0073048,-0.01419462,0.05468284,0.04465481,-0.02964136,-0.01207883,-0.01225547,-0.06202747,-0.04741886,-0.02110766,-0.06189992,-0.00829064,-0.01083772,-0.06163239,0.02909736,-0.01889304,-0.0173963,-0.00692772,0.04724338,0.02112903,0.00186773,0.05436207,-0.00036831,-0.06590398,0.0036183,-0.03393818,0.01194697,-0.02569761,-0.04137276,0.06114973,0.01942693,0.00894857,0.02441227,-0.02888084,-0.01132287,0.04608067,-0.0639055,0.04668551,-0.02960309,-0.05072839,0.03072101,-0.03764351,0.01875121,0.05918803,-0.0184405,-0.02309586,-0.01749806,-0.03248733,-0.0009781,-0.01302317,-0.01257662,-0.04683524,0.01793082,0.05738189,0.05477542,-0.02909917,-0.01957439,0.02598215,0.011954,0.03771475,-0.02117344,-0.02982875,0.00012851,0.03384155,0.06296749,0.00626955,-0.043779,0.0558503,-0.0050177,-0.00295862,-0.02223757,-0.00058746,-0.05113462,-0.04312989,-0.07721082,-0.03734123,0.0154644,-0.00653558,0.10446789,-0.02197088,-0.00299212,0.06069771,-0.02421471,-0.06830654,-0.01790963,-0.03258421,-0.00011762,-0.03567653,-0.0012621,-0.00463157,0.02338306,0.01301362,-0.0327014,-0.06039416,-0.10646969,0.02947715,-0.03327324,-0.00759517,-0.01027449,0.04623373,-0.01017866,-0.02079345,-0.00615429,-0.00007895,0.02074252,0.02811624,0.02698882,-0.03000984,0.04499877,0.01173126,-0.00662477,-0.01025906,-0.03400788,-0.03963593,0.00055082,-0.04328902,-0.04027897,-0.0116679,0.01667154,-0.00538252,-0.03114965,-0.01335824,-0.02781616,-0.07183786,-0.03461871,-0.05133111,0.07695073,0.07352881,-0.02162967,0.0762933,0.02132022,-0.06117398,-0.01020789,-0.02028091,-0.01508321,-0.07897029,0.03087171,0.00380844,-0.04748329,-0.01791081,0.02826604,-0.00070526,0.05315427,0.01895694,-0.00334923,-0.05150174,-0.02519571,-0.01135621,-0.00283417,0.03654024,-0.00985278,-0.04075485,0.03280212,-0.06694091,0.02624604,0.03594726,0.01760647,0.02779192,-0.01145855,-0.00809712,0.00470889,-0.06737162,-0.01364095,-0.00483318,0.00871595,0.02716362,-0.06239572,0.02241279,0.02093405,0.02453795,-0.00235351,0.04558321,0.04054147,0.00182623,-0.0265801,0.01573074,-0.05197823,0.00737904,0.02904082,0.05094672,0.02216692,0.02008267,0.01717059,0.00425494,0.02404857,0.01653608,0.0339811,-0.06146878,0.02396741,0.03418253,0.03259028,-0.01006244,-0.01816133,0.00381001,0.04069557,0.00968056,0.03181721,-0.00644604,-0.012608,0.04884051,-0.01625295,0.01377685,-0.00118217,-0.01280459,0.04580238,0.0147943,0.00372196,0.03571532,-0.06893574,-0.05871705,-0.03806154,0.05189608,-0.02283042,0.04578572,-0.05327821,0.03011153,0.02477166,0.0633257,-0.03039624,-0.01148293,0.04884559,-0.00045812,-0.00754515,-0.01519949,-0.02181038,-0.03644947,0.07951478,0.03210844,-0.00672127,-0.03935953,0.02698702,-0.03833231,0.02397527,0.00999956,0.04283027,0.07624533,0.00594807,-0.01237332,-0.01150226,0.01167159,-0.00470079,0.01150612,0.01469633,-0.00796577,0.03719933,0.00534873,-0.03211283,-0.03324333,0.01580721,0.00992273,-0.02702346,-0.04976952,0.04544617,0.04150331,0.00091253,0.05025431,-0.05820569,-0.0357485,0.00585539,0.02662344,-0.03184935,0.00930728,-0.00743639,-0.01366212,0.00093233,0.02186742,0.00687126,-0.05188348,-0.02598073,0.01657932,0.02575974,-0.04139149,0.0377074,0.01446437,-0.03679452,0.08210654,0.00948136,-0.0246185,-0.04057712,-0.03760802,0.0366505,0.00740652,0.0439129,-0.06190477,0.01012364,-0.00664642,0.02222832,0.01725953,-0.03973181,-0.00715039,-0.06279254,0.0772633,0.04094595,-0.00103932,-0.00825075,-0.03941273,0.00866064,-0.04679428,0.02573706,0.01046342,0.00663062,0.02237637,-0.04169243,0.04013599,-0.0322262,0.00675251,-0.02562449,0.06364278,0.03118881,0.00685146,0.02850297,-0.02199715,0.03520136,-0.01417214,0.02743797,0.02926175,0.00644541,-0.04201528,-0.00504097,-0.03697295,-0.01024799,-0.0540136,0.02782189,-0.02998181,0.03868098,0.05407613,0.0028096,0.03043884,-0.02690299,-0.03908952,-0.10418337,-0.03926798,0.00599891,0.11510529,-0.02646782,-0.00849258,0.02017601,0.04145478,0.03938355,0.04568119,0.00826495,0.02456637,-0.01613876,0.02869871,0.04304136,-0.03292267,0.00633685,0.02237094,-0.02561484,0.00703471,-0.03108824,-0.01375423,0.01988078,0.02362161,0.029409,-0.04799101,-0.03776634,0.07798685,0.01026334,-0.05178254,-0.0002192,0.00304598,0.0197353,-0.00089161,0.06686684,-0.03059649,-0.00016276,-0.0138488,0.04164592,-0.03209724,-0.01947774,0.0154019,-0.03189505,-0.01454453,-0.06065388,-0.01347749,-0.02740557,0.01830539,0.0073578,-0.06037224,0.06202298,0.0162443,0.01820666,0.03333373,0.01342325,-0.01283218,-0.00603417,-0.02251443,-0.00277372,0.00796797,0.01889705,0.02443945,-0.01339377,-0.07682347,0.02988021,-0.05201978,0.00623398,0.00336895,0.02371454,-0.00897794,0.03179828,0.00222578,0.00493432,-0.07176872,-0.04592353,-0.01562799,-0.02884093,-0.02106188,-0.00227021,-0.05763871,0.00636177,-0.04201351,-0.03162162,-0.03670413,-0.02111177,-0.0372644,-0.00861009,0.02464395,0.03895107,0.00741031,-0.00122085,0.00719419,-0.08038905,0.05713343,-0.04671045,0.04990847,0.0458668,-0.03812525,-0.02119777,0.00368237,0.00570199,0.07851907,-0.02689855,0.01103299,0.00760387,-0.00150426,0.03108317,0.04385658,-0.01822916,-0.01814933,-0.00318603,0.00192029,-0.00936792,0.01161807,0.02406413,-0.04447991,-0.01053073,0.04167484,0.04845482,0.00818097,0.04779435,-0.00010311,0.01022246,0.0441646,-0.05778487,-0.03836511,-0.00822081,-0.01667963,-0.02265776,-0.01711912,-0.00706535,0.01913857,-0.00913854,0.04706481,-0.00431792,-0.02966101,-0.02840469,0.0319128,-0.06011508,-0.06429279,0.05311573,0.04221371,8e-7,-0.00879086,0.08300111,0.0284709,0.03520379,0.04853778,-0.0760972,-0.01959855,0.0257097,0.02513619],"last_embed":{"hash":"17ad187","tokens":574}}},"text":null,"length":0,"last_read":{"hash":"17ad187","at":1749002758123},"key":"Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/软件管理器/Powershell.md#自动任务编写#设置自动任务","lines":[105,168],"size":1490,"outlinks":[{"title":"windows系统","target":"windows系统","line":4},{"title":"425","target":"Pasted image 20240410193444.png","line":6},{"title":"如何新建ps1任务","target":"https://blog.csdn.net/wyounger/article/details/77718374","line":9}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/软件管理器/Powershell.md#自动任务编写#设置自动任务#Powershell 自动任务": {"path":null,"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":[0.01715207,-0.03406553,-0.02642786,-0.01384678,-0.03556198,0.01970644,-0.00272134,-0.00087103,0.01708321,-0.09971292,-0.00201796,0.00759755,-0.0323584,-0.01651875,-0.00963777,0.05543854,-0.07184699,0.02013816,-0.0577792,-0.06727336,-0.04410196,-0.02881509,0.032667,-0.01779959,-0.0050394,0.01871368,0.05401367,0.00859556,-0.01643813,0.00513106,0.09054327,-0.0148982,-0.04507171,0.01490341,-0.0291582,-0.02877057,-0.00143817,0.01791058,0.07575357,-0.03091012,-0.01897934,-0.0055715,-0.00823596,0.00436875,-0.08089375,0.06930812,-0.05316829,0.04013446,0.01540568,-0.00984566,-0.00289492,-0.01162674,0.02488689,0.00881491,-0.01038049,-0.02670649,-0.00209976,0.04802245,-0.05833,-0.02500674,-0.01167729,-0.08466418,0.05253244,0.03586923,0.00147421,0.03642694,-0.03613552,-0.03701718,-0.01158804,-0.02328002,0.00675711,0.00723076,0.04400784,-0.07089912,0.0067828,0.02098114,0.00171394,-0.00014696,-0.00632163,0.04671066,-0.07399626,-0.05301555,-0.04132077,0.05270752,0.00540027,0.01172017,0.04359708,-0.04809796,-0.01704848,-0.0159079,0.01023499,-0.04062636,-0.10468787,-0.04448395,-0.01477357,0.01817496,0.04598646,-0.03420395,-0.03937551,-0.04481047,-0.08416011,-0.02625702,-0.03504003,-0.00364268,-0.03597042,-0.07382564,-0.04483867,0.01815899,-0.04844108,-0.02973877,0.02557874,0.03310032,-0.00070185,-0.00553345,0.00409872,-0.03690143,-0.01630924,0.07062511,-0.03403363,0.01691277,0.05182282,-0.01649508,0.00475298,-0.03799722,0.02908354,0.01596035,-0.00136769,-0.08692782,0.04170308,-0.00944011,0.00968502,0.01356664,-0.01757162,0.0745045,-0.03959779,0.02266359,0.06837593,-0.00833004,0.00316439,-0.00318931,0.06020011,-0.01647622,-0.02297121,-0.0397949,0.0598119,0.02473412,0.01192298,0.00920434,0.00125989,0.00261083,0.00623527,0.06440492,0.00407617,-0.02298344,-0.0527299,0.01997143,0.05184954,0.00165499,-0.00286037,-0.05003822,-0.0228026,-0.00805751,0.01641038,0.0065468,-0.01765631,0.02168479,0.0060782,0.00424781,-0.01620781,-0.03336622,-0.05390897,0.03676577,-0.02598509,-0.01750045,-0.0182827,0.02445185,0.00645383,0.03815334,0.02993773,0.00548565,0.02075846,-0.02142806,0.01030386,-0.00710857,-0.03388406,0.09094059,0.03135155,0.06096388,0.03884088,0.04842449,0.01688125,0.02788737,0.01162122,0.02041391,0.05999124,-0.01248247,0.00394048,0.00675156,0.02995191,0.04286026,-0.04693244,-0.04343384,0.00780095,-0.02532578,0.06436855,0.06993123,0.01143563,-0.02453209,0.00631966,-0.03925568,0.01846894,0.04640022,0.09209165,0.0167201,-0.00523286,-0.00607493,-0.02857452,-0.02530721,0.07138668,-0.0828127,0.0352281,-0.04052481,-0.00232814,-0.02235526,-0.03132391,-0.02569296,0.0248303,0.00568379,0.00745953,-0.03702975,0.0175342,0.09431826,0.01235732,-0.05186364,-0.02451813,0.0478466,0.06588542,0.06202812,0.02786793,-0.00783133,0.00626003,-0.05216863,-0.0254274,0.00058552,-0.02115408,-0.02257263,-0.01035207,0.05809425,-0.02696436,0.04633706,0.00964478,-0.03801773,-0.00769902,0.01533749,-0.03443281,-0.06328943,0.00632096,0.08126604,-0.01996407,-0.06860929,-0.00004435,-0.02217569,-0.05071821,0.07532054,0.0053625,-0.01005917,-0.03244455,0.01688979,0.03033059,0.02171998,0.01984099,-0.03484197,-0.02704467,0.01255559,-0.02074565,0.00272006,0.08940203,-0.02259211,-0.00770565,0.00764932,0.01078765,-0.00556948,0.00022478,0.02804547,0.07064211,0.02184214,-0.00690621,-0.0097677,0.0514426,0.01732311,-0.02652653,-0.01043451,-0.00715255,-0.05651894,-0.0689045,-0.02492591,-0.07609601,-0.02193827,-0.00200009,-0.08238536,0.01058663,-0.00901203,-0.01145866,0.00687256,0.06630398,0.02175953,0.00173236,0.05545716,0.00289648,-0.07014269,0.00549629,-0.03819132,0.01526734,-0.02694968,-0.04523468,0.0660174,0.01957288,0.01160188,0.02430417,-0.00960623,0.00112679,0.05026178,-0.06477852,0.05332062,-0.0295661,-0.04893998,0.046905,-0.03016828,0.01619526,0.06740487,-0.02906501,-0.00358929,-0.00315631,-0.01588975,0.01147354,-0.00760879,-0.01880307,-0.05574511,0.02026118,0.03994736,0.03855212,-0.03706008,0.01362344,0.0092773,0.01679337,0.04049211,-0.03870309,-0.03264226,0.00739016,0.02102998,0.08489676,0.00432848,-0.05210204,0.04960933,-0.00383542,-0.00338235,-0.01746587,0.0062344,-0.04010966,-0.06678485,-0.07616414,-0.03930677,0.0136333,0.0047396,0.08392068,-0.02773666,-0.01336192,0.05519583,-0.02228051,-0.07121139,-0.03409532,-0.03412984,-0.0070194,-0.05305348,-0.00772975,-0.00081461,0.0160087,0.01446209,-0.04995912,-0.05606646,-0.10496043,0.03198469,-0.04847248,-0.01170868,-0.00188546,0.03713234,-0.00163654,-0.04210624,0.01119764,-0.0015188,0.01553724,0.02681383,0.01118419,-0.01929817,0.05452035,0.00845708,0.01452818,-0.00637058,-0.05658637,-0.02670412,0.01436824,-0.04845842,-0.04275939,-0.02223478,0.02358868,-0.00251968,-0.02615667,-0.01164459,-0.03047283,-0.07144635,-0.04799078,-0.03240323,0.05776017,0.05829318,-0.01189848,0.08290507,0.01907154,-0.05240341,-0.01015783,-0.03890023,-0.0356298,-0.08333202,0.03520054,-0.00898851,-0.04210186,-0.01209325,0.0251976,0.00873656,0.0567979,0.01341029,-0.00624185,-0.04922123,-0.01052109,-0.0091239,-0.00009786,0.02301846,-0.01897496,-0.04649152,0.03256272,-0.06921687,0.02033267,0.02637895,-0.01469439,0.02296224,-0.01377986,-0.00367695,0.01296159,-0.05890543,-0.00813334,-0.00238591,0.00796296,0.00160532,-0.06678675,0.02038799,0.01713349,0.02397066,-0.00198709,0.02030279,0.02015592,-0.01885481,-0.01173753,0.01859407,-0.05298112,0.03717463,0.0196044,0.03746862,0.00521399,0.00616198,0.00981564,0.00238932,0.02790934,0.00752414,0.04824137,-0.05182523,0.04053234,0.04044733,0.01348352,-0.00768578,-0.03444237,0.00106655,0.04646787,-0.00157073,0.01930862,0.01426663,-0.01568654,0.01581574,-0.01366911,0.02271341,-0.00813372,-0.02352301,0.0315637,0.03184095,-0.00057076,0.05912663,-0.06003036,-0.06498318,-0.02557942,0.06185332,-0.03450425,0.05068643,-0.0456681,0.0330973,0.01870955,0.06247782,-0.0284568,-0.00209379,0.03926654,-0.00407284,0.00170122,-0.00382402,-0.03068942,-0.03540852,0.08886576,0.04464436,-0.00213389,-0.02161809,0.0241577,-0.02914743,0.02217849,-0.01079676,0.01944357,0.07426892,0.02005791,-0.00948289,-0.00997509,0.02509604,-0.00064811,0.00392754,0.02720425,0.02161453,0.03418878,0.01235366,-0.01536685,-0.01579288,0.01396879,0.01073132,-0.04017389,-0.04584203,0.04805098,0.0351249,0.00395999,0.05942404,-0.05628804,-0.02541951,0.00349317,0.02485825,-0.03670302,0.00466249,-0.0100838,-0.00969185,0.00399683,0.0143286,0.01286702,-0.06544038,-0.02037934,0.02647427,0.02429789,-0.05002679,0.02751157,0.03048197,-0.04129028,0.06326336,0.01589727,-0.01182599,-0.03061591,-0.02607184,0.04420142,0.00156165,0.01668838,-0.06681566,0.0120532,-0.00768971,0.03855722,0.01841619,-0.03331117,-0.00511773,-0.0535356,0.06315814,0.03109424,-0.00284841,-0.01157971,-0.01502383,0.04318742,-0.04544391,0.01687223,-0.0025162,0.03121026,0.0220665,-0.0486701,0.04370791,-0.02676827,0.01410776,-0.02821923,0.04532826,0.01762323,0.01231703,0.06005187,-0.0209752,0.00942007,-0.01890439,0.043132,0.02904366,0.00425083,-0.03260968,-0.01202202,-0.03948216,-0.00578259,-0.06120031,0.00995592,-0.02031316,0.05439258,0.04069184,-0.00919332,0.05049253,-0.02440537,-0.03505603,-0.08596481,-0.02976895,0.01479656,0.09555379,-0.01802897,-0.00589919,0.01231104,0.05436395,0.03377923,0.02977306,0.00434728,0.02198992,-0.01515785,0.00911909,0.03487067,-0.0202496,0.00077824,0.03122626,-0.03057026,0.00710572,-0.04473351,-0.00901167,0.0194148,0.03638612,0.04171376,-0.05735987,-0.02997696,0.10084485,0.00377982,-0.02284646,0.02261097,0.01535834,0.02478372,0.00842215,0.04588772,-0.01424014,-0.01741389,-0.00416302,0.04733886,-0.02571959,-0.0128079,-0.01569949,-0.02751265,-0.02028415,-0.05954091,-0.01178695,-0.0305908,0.00953772,-0.02181983,-0.05658601,0.05738665,0.00232949,0.01651957,0.01175897,0.02230874,-0.00012396,-0.00373511,-0.01473779,-0.00551076,-0.00136914,0.01459479,0.02990188,0.01372058,-0.05915832,0.02362972,-0.04540804,-0.00604602,0.01281202,0.02136539,0.01425738,0.05233168,0.00790423,0.02109309,-0.06681161,-0.07185248,0.0087746,-0.02841185,-0.00491939,0.01203992,-0.0564345,0.01386889,-0.0342429,-0.02783263,-0.02605541,-0.01906577,-0.02361667,-0.03148496,0.00066922,0.02118624,0.01820015,0.00219907,0.01642821,-0.06556978,0.05709265,-0.03299094,0.03733901,0.03487769,-0.06459938,-0.02943696,0.02039174,0.02132091,0.07991772,-0.0171336,0.00623594,-0.00223237,-0.011541,0.01842305,0.0640132,-0.03643855,-0.01914185,0.02286449,0.01678409,-0.00491198,0.01341614,0.01384436,-0.03561174,0.00290433,0.03945123,0.06574057,0.003677,0.03730622,0.00781306,0.01408359,0.04002499,-0.05577346,-0.02436083,-0.02257627,-0.02189072,-0.00555976,-0.01206907,0.01368659,0.01536778,-0.02491507,0.0611796,0.00078152,-0.02226724,-0.04121839,0.02897586,-0.06209714,-0.05346905,0.03860181,0.04167356,8e-7,-0.01242197,0.094118,0.02992809,0.03128342,0.05777365,-0.08528128,-0.03339427,0.04412206,0.01326062],"last_embed":{"hash":"s0qrf8","tokens":428}}},"text":null,"length":0,"last_read":{"hash":"s0qrf8","at":1749002758184},"key":"Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/软件管理器/Powershell.md#自动任务编写#设置自动任务#Powershell 自动任务","lines":[119,168],"size":1141,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/软件管理器/Powershell.md#---frontmatter---": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/软件管理器/Powershell.md#---frontmatter---","lines":[1,8],"size":93,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/软件管理器/Powershell.md#": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/软件管理器/Powershell.md#","lines":[10,34],"size":420,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/软件管理器/Powershell.md#简介": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/软件管理器/Powershell.md#简介","lines":[35,44],"size":234,"outlinks":[{"title":"windows系统","target":"windows系统","line":4}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/软件管理器/Powershell.md#简介#{1}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/软件管理器/Powershell.md#简介#{1}","lines":[36,36],"size":31,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/软件管理器/Powershell.md#简介#{2}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/软件管理器/Powershell.md#简介#{2}","lines":[37,37],"size":28,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/软件管理器/Powershell.md#简介#{3}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/软件管理器/Powershell.md#简介#{3}","lines":[38,40],"size":86,"outlinks":[{"title":"windows系统","target":"windows系统","line":1}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/软件管理器/Powershell.md#简介#{4}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/软件管理器/Powershell.md#简介#{4}","lines":[41,42],"size":76,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/软件管理器/Powershell.md#简介#{5}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/软件管理器/Powershell.md#简介#{5}","lines":[43,44],"size":4,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/软件管理器/Powershell.md#技术实现": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/软件管理器/Powershell.md#技术实现","lines":[45,64],"size":685,"outlinks":[{"title":".NET","target":"NET Framework","line":3},{"title":".NET","target":"NET Framework","line":3},{"title":"终端","target":"终端","line":9},{"title":"Neovim","target":"Neovim","line":10},{"title":"SSH协议","target":"SSH协议","line":10},{"title":"windows系统","target":"windows系统","line":11},{"title":"cmdlet","target":"cmdlet","line":13},{"title":"powershell的历史","target":"powershell的历史","line":14}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/软件管理器/Powershell.md#技术实现#建立在NET Framework框架之上": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/软件管理器/Powershell.md#技术实现#建立在NET Framework框架之上","lines":[46,64],"size":678,"outlinks":[{"title":".NET","target":"NET Framework","line":2},{"title":".NET","target":"NET Framework","line":2},{"title":"终端","target":"终端","line":8},{"title":"Neovim","target":"Neovim","line":9},{"title":"SSH协议","target":"SSH协议","line":9},{"title":"windows系统","target":"windows系统","line":10},{"title":"cmdlet","target":"cmdlet","line":12},{"title":"powershell的历史","target":"powershell的历史","line":13}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/软件管理器/Powershell.md#技术实现#建立在NET Framework框架之上#{1}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/软件管理器/Powershell.md#技术实现#建立在NET Framework框架之上#{1}","lines":[47,51],"size":287,"outlinks":[{"title":".NET","target":"NET Framework","line":1},{"title":".NET","target":"NET Framework","line":1}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/软件管理器/Powershell.md#技术实现#建立在NET Framework框架之上#{2}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/软件管理器/Powershell.md#技术实现#建立在NET Framework框架之上#{2}","lines":[52,55],"size":117,"outlinks":[{"title":"终端","target":"终端","line":2},{"title":"Neovim","target":"Neovim","line":3},{"title":"SSH协议","target":"SSH协议","line":3},{"title":"windows系统","target":"windows系统","line":4}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/软件管理器/Powershell.md#技术实现#建立在NET Framework框架之上#{3}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/软件管理器/Powershell.md#技术实现#建立在NET Framework框架之上#{3}","lines":[56,56],"size":3,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/软件管理器/Powershell.md#技术实现#建立在NET Framework框架之上#{4}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/软件管理器/Powershell.md#技术实现#建立在NET Framework框架之上#{4}","lines":[57,57],"size":45,"outlinks":[{"title":"cmdlet","target":"cmdlet","line":1}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/软件管理器/Powershell.md#技术实现#建立在NET Framework框架之上#{5}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/软件管理器/Powershell.md#技术实现#建立在NET Framework框架之上#{5}","lines":[58,58],"size":28,"outlinks":[{"title":"powershell的历史","target":"powershell的历史","line":1}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/软件管理器/Powershell.md#技术实现#建立在NET Framework框架之上#{6}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/软件管理器/Powershell.md#技术实现#建立在NET Framework框架之上#{6}","lines":[59,59],"size":28,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/软件管理器/Powershell.md#技术实现#建立在NET Framework框架之上#{7}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/软件管理器/Powershell.md#技术实现#建立在NET Framework框架之上#{7}","lines":[60,60],"size":76,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/软件管理器/Powershell.md#技术实现#建立在NET Framework框架之上#{8}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/软件管理器/Powershell.md#技术实现#建立在NET Framework框架之上#{8}","lines":[61,62],"size":58,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/软件管理器/Powershell.md#技术实现#建立在NET Framework框架之上#{9}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/软件管理器/Powershell.md#技术实现#建立在NET Framework框架之上#{9}","lines":[63,64],"size":4,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/软件管理器/Powershell.md#PowerShell优点": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/软件管理器/Powershell.md#PowerShell优点","lines":[65,79],"size":212,"outlinks":[{"title":"内存","target":"内存","line":5}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/软件管理器/Powershell.md#PowerShell优点#{1}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/软件管理器/Powershell.md#PowerShell优点#{1}","lines":[67,68],"size":24,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/软件管理器/Powershell.md#PowerShell优点#{2}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/软件管理器/Powershell.md#PowerShell优点#{2}","lines":[69,70],"size":39,"outlinks":[{"title":"内存","target":"内存","line":1}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/软件管理器/Powershell.md#PowerShell优点#{3}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/软件管理器/Powershell.md#PowerShell优点#{3}","lines":[71,72],"size":33,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/软件管理器/Powershell.md#PowerShell优点#{4}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/软件管理器/Powershell.md#PowerShell优点#{4}","lines":[73,74],"size":32,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/软件管理器/Powershell.md#PowerShell优点#{5}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/软件管理器/Powershell.md#PowerShell优点#{5}","lines":[75,76],"size":36,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/软件管理器/Powershell.md#PowerShell优点#{6}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/软件管理器/Powershell.md#PowerShell优点#{6}","lines":[77,78],"size":23,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/软件管理器/Powershell.md#PowerShell优点#{7}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/软件管理器/Powershell.md#PowerShell优点#{7}","lines":[79,79],"size":3,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/软件管理器/Powershell.md#常用操作": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/软件管理器/Powershell.md#常用操作","lines":[80,98],"size":280,"outlinks":[{"title":"Windows的执行策略","target":"Windows的执行策略","line":7}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/软件管理器/Powershell.md#常用操作#{1}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/软件管理器/Powershell.md#常用操作#{1}","lines":[82,85],"size":102,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/软件管理器/Powershell.md#常用操作#{2}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/软件管理器/Powershell.md#常用操作#{2}","lines":[86,86],"size":26,"outlinks":[{"title":"Windows的执行策略","target":"Windows的执行策略","line":1}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/软件管理器/Powershell.md#常用操作#{3}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/软件管理器/Powershell.md#常用操作#{3}","lines":[87,91],"size":10,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/软件管理器/Powershell.md#常用操作#获取当前在线的用户": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/软件管理器/Powershell.md#常用操作#获取当前在线的用户","lines":[92,98],"size":130,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/软件管理器/Powershell.md#常用操作#获取当前在线的用户#{1}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/软件管理器/Powershell.md#常用操作#获取当前在线的用户#{1}","lines":[93,95],"size":100,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/软件管理器/Powershell.md#常用操作#获取当前在线的用户#{2}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/软件管理器/Powershell.md#常用操作#获取当前在线的用户#{2}","lines":[96,98],"size":15,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/软件管理器/Powershell.md#自动任务编写#{1}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/软件管理器/Powershell.md#自动任务编写#{1}","lines":[101,104],"size":162,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/软件管理器/Powershell.md#自动任务编写#设置自动任务#Task Scheduler": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/软件管理器/Powershell.md#自动任务编写#设置自动任务#Task Scheduler","lines":[107,118],"size":337,"outlinks":[{"title":"windows系统","target":"windows系统","line":2},{"title":"425","target":"Pasted image 20240410193444.png","line":4},{"title":"如何新建ps1任务","target":"https://blog.csdn.net/wyounger/article/details/77718374","line":7}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/软件管理器/Powershell.md#自动任务编写#设置自动任务#Task Scheduler#{1}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/软件管理器/Powershell.md#自动任务编写#设置自动任务#Task Scheduler#{1}","lines":[108,108],"size":54,"outlinks":[{"title":"windows系统","target":"windows系统","line":1}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/软件管理器/Powershell.md#自动任务编写#设置自动任务#Task Scheduler#{2}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/软件管理器/Powershell.md#自动任务编写#设置自动任务#Task Scheduler#{2}","lines":[109,109],"size":15,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/软件管理器/Powershell.md#自动任务编写#设置自动任务#Task Scheduler#{3}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/软件管理器/Powershell.md#自动任务编写#设置自动任务#Task Scheduler#{3}","lines":[110,110],"size":40,"outlinks":[{"title":"425","target":"Pasted image 20240410193444.png","line":1}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/软件管理器/Powershell.md#自动任务编写#设置自动任务#Task Scheduler#{4}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/软件管理器/Powershell.md#自动任务编写#设置自动任务#Task Scheduler#{4}","lines":[111,113],"size":137,"outlinks":[{"title":"如何新建ps1任务","target":"https://blog.csdn.net/wyounger/article/details/77718374","line":3}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/软件管理器/Powershell.md#自动任务编写#设置自动任务#Task Scheduler#{5}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/软件管理器/Powershell.md#自动任务编写#设置自动任务#Task Scheduler#{5}","lines":[114,117],"size":64,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/软件管理器/Powershell.md#自动任务编写#设置自动任务#Task Scheduler#{6}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/软件管理器/Powershell.md#自动任务编写#设置自动任务#Task Scheduler#{6}","lines":[118,118],"size":3,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/软件管理器/Powershell.md#自动任务编写#设置自动任务#Powershell 自动任务#{1}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/软件管理器/Powershell.md#自动任务编写#设置自动任务#Powershell 自动任务#{1}","lines":[120,122],"size":171,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/软件管理器/Powershell.md#自动任务编写#设置自动任务#Powershell 自动任务#{2}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/软件管理器/Powershell.md#自动任务编写#设置自动任务#Powershell 自动任务#{2}","lines":[123,123],"size":35,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/软件管理器/Powershell.md#自动任务编写#设置自动任务#Powershell 自动任务#{3}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/软件管理器/Powershell.md#自动任务编写#设置自动任务#Powershell 自动任务#{3}","lines":[124,127],"size":102,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/软件管理器/Powershell.md#自动任务编写#设置自动任务#Powershell 自动任务#{4}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/软件管理器/Powershell.md#自动任务编写#设置自动任务#Powershell 自动任务#{4}","lines":[128,128],"size":29,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/软件管理器/Powershell.md#自动任务编写#设置自动任务#Powershell 自动任务#{5}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/软件管理器/Powershell.md#自动任务编写#设置自动任务#Powershell 自动任务#{5}","lines":[129,129],"size":28,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/软件管理器/Powershell.md#自动任务编写#设置自动任务#Powershell 自动任务#{6}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/软件管理器/Powershell.md#自动任务编写#设置自动任务#Powershell 自动任务#{6}","lines":[130,133],"size":70,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/软件管理器/Powershell.md#自动任务编写#设置自动任务#Powershell 自动任务#{7}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/软件管理器/Powershell.md#自动任务编写#设置自动任务#Powershell 自动任务#{7}","lines":[134,134],"size":17,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/软件管理器/Powershell.md#自动任务编写#设置自动任务#Powershell 自动任务#{8}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/软件管理器/Powershell.md#自动任务编写#设置自动任务#Powershell 自动任务#{8}","lines":[135,135],"size":13,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/软件管理器/Powershell.md#自动任务编写#设置自动任务#Powershell 自动任务#{9}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/软件管理器/Powershell.md#自动任务编写#设置自动任务#Powershell 自动任务#{9}","lines":[136,139],"size":176,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/软件管理器/Powershell.md#自动任务编写#设置自动任务#Powershell 自动任务#{10}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/软件管理器/Powershell.md#自动任务编写#设置自动任务#Powershell 自动任务#{10}","lines":[140,140],"size":19,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/软件管理器/Powershell.md#自动任务编写#设置自动任务#Powershell 自动任务#{11}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/软件管理器/Powershell.md#自动任务编写#设置自动任务#Powershell 自动任务#{11}","lines":[141,141],"size":25,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/软件管理器/Powershell.md#自动任务编写#设置自动任务#Powershell 自动任务#{12}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/软件管理器/Powershell.md#自动任务编写#设置自动任务#Powershell 自动任务#{12}","lines":[142,145],"size":77,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/软件管理器/Powershell.md#自动任务编写#设置自动任务#Powershell 自动任务#{13}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/软件管理器/Powershell.md#自动任务编写#设置自动任务#Powershell 自动任务#{13}","lines":[146,146],"size":14,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/软件管理器/Powershell.md#自动任务编写#设置自动任务#Powershell 自动任务#{14}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/软件管理器/Powershell.md#自动任务编写#设置自动任务#Powershell 自动任务#{14}","lines":[147,147],"size":26,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/软件管理器/Powershell.md#自动任务编写#设置自动任务#Powershell 自动任务#{15}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/软件管理器/Powershell.md#自动任务编写#设置自动任务#Powershell 自动任务#{15}","lines":[148,157],"size":186,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/软件管理器/Powershell.md#自动任务编写#设置自动任务#Powershell 自动任务#{16}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/软件管理器/Powershell.md#自动任务编写#设置自动任务#Powershell 自动任务#{16}","lines":[158,158],"size":25,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/软件管理器/Powershell.md#自动任务编写#设置自动任务#Powershell 自动任务#{17}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/软件管理器/Powershell.md#自动任务编写#设置自动任务#Powershell 自动任务#{17}","lines":[159,163],"size":72,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/软件管理器/Powershell.md#自动任务编写#设置自动任务#Powershell 自动任务#{18}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/软件管理器/Powershell.md#自动任务编写#设置自动任务#Powershell 自动任务#{18}","lines":[164,165],"size":13,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/软件管理器/Powershell.md#自动任务编写#设置自动任务#Powershell 自动任务#{19}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/软件管理器/Powershell.md#自动任务编写#设置自动任务#Powershell 自动任务#{19}","lines":[166,168],"size":5,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/软件管理器/Powershell.md#自动任务编写#任务查看": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/软件管理器/Powershell.md#自动任务编写#任务查看","lines":[169,209],"size":688,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/软件管理器/Powershell.md#自动任务编写#任务查看#{1}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/软件管理器/Powershell.md#自动任务编写#任务查看#{1}","lines":[171,209],"size":678,"outlinks":[],"class_name":"SmartBlock"},
"smart_sources:Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/软件管理器/Powershell.md": {"path":"Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/软件管理器/Powershell.md","embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.03906095,-0.00736028,-0.0157197,-0.03542967,-0.03097798,-0.00060986,-0.05953949,0.00831061,0.05610263,-0.00253434,0.00345205,-0.00860766,-0.00900591,0.03057026,0.04122168,0.03057902,0.00215025,0.07917902,-0.06489855,0.00495862,0.06618789,-0.0414101,-0.02107024,-0.07425083,-0.0100829,0.02486019,0.03535479,-0.00811375,-0.05091126,-0.17748356,-0.01802893,-0.02510414,0.01274257,0.01600895,0.056655,-0.03433906,0.01593213,0.03511621,-0.00223604,0.01317371,0.03006768,0.00469066,-0.0053739,0.05536851,-0.04233471,-0.08345295,-0.03689197,-0.01605742,0.01201131,-0.03983977,-0.04379851,-0.04683235,-0.03005309,-0.00775176,-0.05487235,0.02372068,0.00772824,0.00283446,0.09927331,-0.00768765,-0.01182266,0.01131288,-0.22670902,0.08582302,0.05661917,-0.02998092,0.02457799,-0.03211677,-0.03431019,0.02299763,-0.07602929,-0.01522837,-0.04768382,0.06462889,0.05862491,-0.02144335,-0.04827347,-0.01162327,-0.00922794,-0.00766036,0.00575678,0.04028162,0.00276,-0.00627957,-0.02100666,-0.00174577,-0.02585824,0.00227174,0.05549547,0.01617545,-0.00709424,-0.13250493,0.07694226,0.0156947,0.00119304,0.01383374,0.04016612,0.05629029,-0.05340999,0.05955383,-0.05581167,-0.03304848,-0.03544924,-0.0618709,0.02267181,-0.03852351,0.04999403,-0.03071488,-0.02270501,0.00370605,-0.11106731,-0.01304261,0.0420072,-0.04366421,0.04307396,0.02835313,0.01105849,-0.01636876,-0.00381688,0.00348545,-0.00952885,-0.0259746,0.08406639,-0.00421863,-0.00151861,-0.04371706,0.08093251,0.08294919,0.03386225,0.07934539,0.08276318,-0.0278235,-0.04082903,-0.02088661,0.00235192,-0.00317986,-0.0327031,0.02322493,0.03047518,-0.09281853,-0.01244635,-0.00171457,0.00541001,-0.07819991,-0.00984237,0.08851836,-0.06489789,0.0025881,0.01893395,-0.02874423,0.04668138,0.03675411,-0.01775338,-0.05554199,-0.02379975,0.05819992,0.10647466,0.12295765,0.02348702,-0.03171831,-0.01326537,-0.00329335,-0.06828041,0.114319,0.01916705,-0.0497628,-0.03310484,-0.01697562,0.02759947,-0.02496589,0.02111035,-0.02574428,0.02645904,0.02777121,0.04036491,-0.01512925,-0.04387842,-0.01008677,0.0367052,0.03884383,0.05169131,-0.04689619,-0.06394693,0.03439538,0.05298454,-0.09065778,0.00042821,-0.08069039,0.01846126,-0.00735886,-0.07682753,-0.02597669,-0.0406414,-0.03839006,-0.04623104,-0.05594911,0.05687554,0.02289383,0.01976007,-0.01653639,0.08057795,-0.00964087,-0.06130368,-0.02161179,-0.02552022,-0.01092642,0.00426477,-0.001585,0.07334411,0.01315656,0.01531057,-0.02613194,0.02130898,0.00873431,-0.06236612,0.00247075,-0.00515391,-0.02107401,0.06330239,0.03572053,-0.05245897,0.02000228,-0.09953849,-0.20981845,0.01242084,-0.01390498,-0.03171067,0.01002708,-0.0408479,0.03022767,0.04161437,0.0561626,0.04248537,0.08932263,0.02780154,-0.0728225,-0.03534689,0.05436871,0.02135243,0.00529825,-0.02198478,-0.08352496,0.0306302,0.004056,0.05745443,-0.07256681,-0.03956849,0.03709177,-0.03308716,0.09882528,0.03083211,0.03217383,0.02483921,0.05503675,0.0234476,0.04307228,-0.09453272,-0.02120051,0.01273576,-0.02207163,-0.01145919,-0.00934648,0.01434229,-0.01228798,0.0208655,-0.04151063,-0.06948122,-0.01595287,-0.01894557,0.00197698,0.04271203,-0.00429901,0.03400299,-0.02291738,-0.03269163,0.00110738,0.0172786,-0.02645529,-0.02410179,-0.02730541,-0.05967142,-0.04091483,0.04417066,0.02572806,-0.03146617,0.05238059,0.03051204,0.00061809,-0.00511645,0.01893109,-0.00994055,0.02447668,0.00988888,-0.03925674,0.08301426,-0.00638238,-0.00124082,0.05980388,0.01507673,0.00208114,0.0223019,0.03075098,0.00949177,0.06426158,0.01069845,0.03341208,0.00502826,0.03929915,0.00548802,0.02865444,-0.03640899,0.08454178,-0.08477932,-0.07679962,-0.01874831,-0.04802485,0.03250014,0.06763902,-0.0337255,-0.31603172,0.05278291,0.02148047,0.00165986,0.02579219,0.03926852,0.0022675,-0.01286352,-0.03691125,-0.03696388,-0.09080007,0.09218378,-0.02384449,-0.04746155,-0.05105178,-0.02012726,0.05421133,-0.00916393,0.07678001,-0.01417102,0.02100665,0.07232869,0.21659268,0.01853488,0.03396755,0.00933077,-0.02336212,0.03395937,0.10663855,0.03463108,0.04118907,-0.03111611,0.12016929,-0.05969599,0.05983593,0.009861,-0.0518264,0.03542937,0.03769949,0.01311218,-0.04156737,0.0363864,-0.00546504,-0.03250666,0.1047371,0.0174525,0.03515883,-0.04667918,0.00140086,0.04866079,-0.01307089,0.05489568,-0.02203187,0.00284772,0.02818432,0.02592578,0.0169577,-0.04714517,-0.02003881,-0.01878665,0.02169325,-0.00612087,0.09664897,0.08077285,0.06043421],"last_embed":{"hash":"b052e6a46e0f57b9ce72fd569e00df86685a62b8568f577a5510e090a12520ce","tokens":393}},"Xenova/jina-embeddings-v2-base-zh":{"vec":[-0.0462233,-0.02344364,0.03601196,0.02975422,-0.03853555,0.05179002,0.01808045,-0.01936191,-0.02389547,-0.08769491,-0.00263799,0.03459176,-0.02733344,-0.0659572,0.00605074,0.051423,-0.0516023,-0.01903443,-0.08083147,-0.04393337,-0.00109724,-0.00915499,0.0553507,-0.00115934,0.01498405,0.02573782,0.00743403,-0.03166458,-0.02815903,0.00124795,0.06763262,-0.02008253,-0.00679301,0.00094229,0.02612127,0.01646609,0.02745105,0.03155208,0.02387764,-0.01699768,-0.01961652,0.00627809,-0.02829592,-0.01629882,-0.06248883,0.07302245,0.01093685,0.04036095,-0.01293807,0.03370862,0.00818511,-0.025302,0.06107023,0.00058555,-0.01847906,0.00012696,-0.00623989,0.02786696,-0.08424379,0.00502714,-0.028519,-0.02524343,0.03103439,0.01605967,-0.03714433,0.02709816,-0.0143546,-0.01891341,-0.03924932,-0.00043656,0.05299381,0.01221551,0.08976559,-0.0524066,-0.00531277,-0.00716524,0.0110878,-0.01224591,-0.02385175,-0.00260939,-0.05572258,-0.03678579,0.00907386,0.08926047,-0.00043582,-0.03081335,0.02543527,-0.06393993,-0.03440888,0.00513737,-0.01701746,0.01327631,-0.11335742,-0.04459981,0.01139059,0.05411384,0.04648764,-0.06066965,0.00956044,0.00914234,-0.03971686,0.00227758,-0.1065691,-0.00892974,-0.03618002,-0.05721947,-0.04090554,0.08787636,-0.03735353,-0.00878583,0.04740244,0.03364222,-0.06893686,-0.03885696,-0.03566657,-0.02089658,-0.02962723,0.07122062,-0.03097961,-0.00849373,-0.02067895,0.03674321,0.01079032,0.0124291,0.06100539,-0.01679824,-0.01030529,-0.08557525,0.03698665,-0.01276099,0.00319098,0.0607651,-0.00304238,0.06239089,0.0523176,0.04898227,0.03176166,-0.00974105,-0.01770953,-0.00319514,0.10675193,-0.03200647,0.04404661,-0.0277077,0.00412555,-0.00494382,0.01101221,-0.01484754,-0.00324817,0.05013114,-0.01930018,-0.02130827,0.03389492,-0.04270226,0.00013712,-0.02228341,0.03503373,0.00827921,0.0061371,-0.05274631,0.00387399,0.04522111,-0.01603914,0.00287552,0.00828367,0.00688713,-0.02232389,-0.01323589,0.05567719,-0.04601847,-0.00110748,0.03529431,0.01070153,-0.05119127,0.00428818,0.0096441,0.01375549,0.02005934,0.04434392,0.0082346,-0.01359201,0.02344931,0.03400639,-0.00867325,-0.02736093,0.06593109,-0.00090845,0.10012984,0.08975587,0.04446977,0.01902899,0.03636678,0.04143156,-0.00369444,0.04287684,-0.01235803,-0.02387655,-0.05800297,0.05290486,0.07181033,-0.07862076,-0.04806804,-0.01825313,-0.07940293,0.02742981,0.05103453,0.03529676,-0.03415716,0.00693809,-0.0261882,0.00789504,0.02798506,-0.00252813,-0.01474141,0.00457145,-0.02572361,-0.05265841,-0.01007451,0.06374288,-0.05507369,0.01145644,-0.03915229,0.00559851,0.04395472,-0.03915263,-0.05838151,0.015681,0.03093959,-0.02681515,-0.0223757,-0.00060253,0.03382081,0.03566637,-0.03303619,-0.05234758,0.03533246,0.02813094,0.04407249,0.04057437,-0.04305761,-0.00839035,0.01166082,0.02792674,-0.04856225,-0.01500769,0.02536462,0.00342511,0.02819076,-0.03858448,0.07129735,0.05234599,0.01009914,0.03068034,0.01880014,-0.01659831,0.01546793,-0.00327202,0.06672123,0.02084411,-0.05123934,0.00403002,-0.05984211,-0.06962766,0.0613309,0.02994087,-0.04329434,-0.03655006,0.05806902,0.05169844,0.01022957,-0.00914887,-0.00726975,0.00624538,0.01370374,-0.04959084,0.00119726,0.07411009,-0.03464311,0.04457339,0.01641751,-0.00999507,-0.04807617,-0.01637734,0.00678377,0.01201491,0.0328629,0.01653422,-0.02008859,0.07619923,0.06857024,-0.01252263,-0.03979398,-0.0362176,-0.0365543,-0.04851313,-0.00826828,0.01264214,0.01097855,0.00046141,-0.0453662,0.04654053,-0.02607526,-0.05594069,0.01172708,-0.01795145,0.03996135,0.00343543,0.00735585,0.023607,-0.06730923,0.00185367,0.01506677,0.02933706,-0.0518737,-0.03326796,0.06408735,0.00010775,0.01535734,-0.00997953,-0.05579335,-0.05011293,0.04705212,-0.02418217,0.0632529,0.004807,-0.03428956,0.01013766,0.01034908,0.04875112,0.04014981,0.00455415,0.01518953,-0.04870272,-0.08160298,0.00629627,-0.00147235,0.02943056,0.00085031,0.01681659,0.03479184,0.00727785,0.01239435,-0.04504522,0.00818801,0.0182236,0.08736426,-0.00195671,-0.01053397,-0.03939061,0.02103615,0.0298124,-0.02220679,-0.04956385,0.02599541,-0.01231403,0.02549024,-0.00254557,0.02830869,-0.01770745,-0.01436133,-0.08040109,-0.02263248,-0.00325266,0.03608934,0.08430037,-0.03396175,0.02831095,0.01927274,-0.0760969,-0.03783887,-0.01805946,-0.02051482,0.02149048,0.0424908,-0.0335931,-0.00570206,0.00960402,-0.02715268,-0.01417197,-0.03630145,-0.07189945,-0.01068651,-0.02146385,-0.0030132,-0.0306115,0.01856547,-0.05599178,-0.00051023,-0.00517403,-0.00031166,0.04324738,-0.00091527,-0.01309418,-0.04349332,0.06748814,0.02984616,-0.00748277,-0.05563071,0.01205709,-0.05338294,-0.01180428,-0.04551513,-0.05711396,-0.03138139,0.00100885,-0.03674781,-0.05152178,0.02382115,0.00577638,-0.02778213,-0.04145773,0.02069237,0.04060472,0.09369967,-0.05241977,0.02034592,0.01582893,-0.07811794,-0.04875532,0.01775705,0.00896204,-0.04203217,-0.0205367,-0.00610877,-0.07447952,-0.00447176,0.01769652,0.00124661,0.03245739,0.03330415,-0.04490249,-0.0837066,-0.02869472,0.01319884,-0.01977721,0.00637243,0.00995417,-0.04638042,0.00619728,-0.0412889,-0.02430841,0.02736719,-0.00610622,-0.02779355,-0.01620719,0.02856778,0.01024983,-0.0728607,-0.00313217,-0.0102538,-0.01368918,0.01179803,-0.03955169,-0.0557969,0.0300373,0.00458226,-0.01587524,0.0074329,0.02814116,0.02734578,-0.01068927,-0.01254386,-0.02933617,-0.01571866,0.0048343,0.0295192,0.04861349,0.02425288,-0.05591314,0.01699479,0.05442599,-0.03920412,0.03021011,-0.08649381,-0.01508925,0.00717975,0.01315749,-0.00642593,-0.01850482,0.02403298,0.00038264,-0.0439107,-0.02099042,-0.01450917,-0.02208865,0.01489411,-0.00954878,0.02213921,0.018828,-0.03697703,0.00899102,0.02602678,0.0239398,0.03878422,-0.04994747,-0.04457376,-0.04458633,-0.001099,-0.01211541,0.01651555,0.01458049,0.03295834,0.05535305,0.09558187,-0.02876021,0.02473339,0.00558956,-0.01810958,0.01362875,-0.01694408,-0.01085354,-0.01450475,0.09835839,0.00495519,-0.04953573,0.00185876,0.00100878,0.01929541,0.0195311,-0.0320221,0.02251992,0.0639874,-0.00599446,0.01437528,0.00207893,-0.02045386,-0.0090741,0.03330286,0.01421737,0.00972413,0.01017102,0.02632418,0.00633434,-0.06422357,-0.00043971,0.01654145,0.01639142,-0.04077725,-0.00888875,0.00811879,-0.05243327,0.05265041,0.00263938,0.02183796,-0.01622161,0.06006761,-0.01519971,0.00340466,-0.01620105,-0.01681531,0.01003119,0.01202814,-0.02579924,0.00076673,0.05121831,0.03917054,0.00812186,-0.05496028,0.00362785,0.00886431,-0.0196869,0.03454256,0.02730414,-0.01362113,-0.0004839,-0.03243915,0.03833065,-0.04829328,0.02154141,-0.01930417,-0.02977075,-0.00227541,0.03455544,-0.00284881,-0.02734025,0.01596264,-0.08365693,0.05518698,0.05359305,-0.02372401,-0.00817902,-0.0324179,0.03616475,-0.02315564,0.01233937,-0.00161007,0.01426736,0.04642434,0.01375635,0.01892707,-0.00158607,-0.03224469,-0.02063129,0.02549521,0.08786323,-0.03196165,-0.05434465,0.01758378,0.0045912,-0.02339578,-0.01005282,0.04265252,0.03562998,-0.04968836,-0.0235219,-0.03373653,-0.00902893,-0.02602961,0.00653662,-0.05970012,0.02131114,0.03796284,0.02396744,-0.03798785,-0.00301345,0.01870818,-0.0895652,-0.0225387,-0.00510213,0.07713165,0.00365787,0.03464051,0.01896351,0.00882055,0.04499507,0.05288957,0.0210395,0.03230748,-0.00936236,0.00787595,0.03080702,-0.02013073,0.02384152,0.05174675,-0.0185587,0.00061845,-0.08218265,0.0327995,0.01536171,-0.00929302,0.02965291,-0.03963016,-0.03439198,0.05107952,0.03493161,-0.04359963,-0.05299625,-0.0365428,-0.03277323,-0.04316487,0.04890797,-0.0061231,0.01387155,-0.03497647,0.0058855,-0.01033874,-0.05003775,0.01108171,-0.02450769,-0.08145075,-0.04648667,-0.02814452,-0.02542421,0.02438415,0.04414027,-0.05459373,0.04101127,-0.00284619,-0.05776976,0.0246818,-0.02025168,-0.02382218,-0.01209898,0.01120001,-0.0184238,0.02380077,-0.02057998,0.00338481,-0.01015327,-0.07530049,0.00637944,-0.03029389,0.03635069,0.0084508,0.0369613,0.00685522,0.03857859,-0.06511568,0.03563783,-0.05206428,-0.03098044,-0.0473591,-0.00667283,-0.02440425,-0.02211127,-0.01251514,0.00773565,0.01716113,-0.02084854,-0.01027074,0.00791502,-0.02539482,-0.01928503,0.00653369,0.06326154,0.02410082,-0.00464811,0.01267386,-0.02445105,0.05194762,-0.05154487,0.0696478,0.04894178,-0.04785072,0.04044757,0.00718615,0.00365274,0.01525705,-0.01049574,0.04739937,-0.01351852,0.03123724,0.00401435,0.06200679,-0.00221805,-0.01359456,-0.03149905,0.01742053,0.0209644,0.00811402,0.01414317,-0.05402755,0.01829154,0.02845719,0.04306484,0.00589791,-0.02730802,-0.0292708,0.04055259,0.06600253,-0.03021361,-0.04161208,-0.00478002,-0.02596358,-0.0769573,-0.04685478,0.00691848,0.00950237,-0.00528031,0.06314019,-0.01573955,0.00956687,-0.00984503,0.04670051,-0.02132389,-0.03676693,0.02165639,-0.00388435,9.8e-7,-0.01045828,0.05043177,0.02068589,-0.01037451,-0.05392688,-0.04372225,0.00485001,0.03114565,0.00764018],"last_embed":{"tokens":1583,"hash":"103kd6v"}}},"last_read":{"hash":"103kd6v","at":1750737384474},"class_name":"SmartSource","outlinks":[{"title":"windows系统","target":"windows系统","line":38},{"title":".NET","target":"NET Framework","line":47},{"title":".NET","target":"NET Framework","line":47},{"title":"终端","target":"终端","line":53},{"title":"Neovim","target":"Neovim","line":54},{"title":"SSH协议","target":"SSH协议","line":54},{"title":"windows系统","target":"windows系统","line":55},{"title":"cmdlet","target":"cmdlet","line":57},{"title":"powershell的历史","target":"powershell的历史","line":58},{"title":"内存","target":"内存","line":69},{"title":"Windows的执行策略","target":"Windows的执行策略","line":86},{"title":"windows系统","target":"windows系统","line":108},{"title":"425","target":"Pasted image 20240410193444.png","line":110},{"title":"如何新建ps1任务","target":"https://blog.csdn.net/wyounger/article/details/77718374","line":113}],"metadata":{"tags":["操作系统/windows/终端"],"aliases":["Windows PowerShell"],"cssclasses":["editor-full"]},"blocks":{"#---frontmatter---":[1,8],"#":[10,34],"#简介":[35,44],"#简介#{1}":[36,36],"#简介#{2}":[37,37],"#简介#{3}":[38,40],"#简介#{4}":[41,42],"#简介#{5}":[43,44],"#技术实现":[45,64],"#技术实现#建立在NET Framework框架之上":[46,64],"#技术实现#建立在NET Framework框架之上#{1}":[47,51],"#技术实现#建立在NET Framework框架之上#{2}":[52,55],"#技术实现#建立在NET Framework框架之上#{3}":[56,56],"#技术实现#建立在NET Framework框架之上#{4}":[57,57],"#技术实现#建立在NET Framework框架之上#{5}":[58,58],"#技术实现#建立在NET Framework框架之上#{6}":[59,59],"#技术实现#建立在NET Framework框架之上#{7}":[60,60],"#技术实现#建立在NET Framework框架之上#{8}":[61,62],"#技术实现#建立在NET Framework框架之上#{9}":[63,64],"#PowerShell优点":[65,79],"#PowerShell优点#{1}":[67,68],"#PowerShell优点#{2}":[69,70],"#PowerShell优点#{3}":[71,72],"#PowerShell优点#{4}":[73,74],"#PowerShell优点#{5}":[75,76],"#PowerShell优点#{6}":[77,78],"#PowerShell优点#{7}":[79,79],"#常用操作":[80,98],"#常用操作#{1}":[82,85],"#常用操作#{2}":[86,86],"#常用操作#{3}":[87,91],"#常用操作#获取当前在线的用户":[92,98],"#常用操作#获取当前在线的用户#{1}":[93,95],"#常用操作#获取当前在线的用户#{2}":[96,98],"#自动任务编写":[99,209],"#自动任务编写#{1}":[101,104],"#自动任务编写#设置自动任务":[105,168],"#自动任务编写#设置自动任务#Task Scheduler":[107,118],"#自动任务编写#设置自动任务#Task Scheduler#{1}":[108,108],"#自动任务编写#设置自动任务#Task Scheduler#{2}":[109,109],"#自动任务编写#设置自动任务#Task Scheduler#{3}":[110,110],"#自动任务编写#设置自动任务#Task Scheduler#{4}":[111,113],"#自动任务编写#设置自动任务#Task Scheduler#{5}":[114,117],"#自动任务编写#设置自动任务#Task Scheduler#{6}":[118,118],"#自动任务编写#设置自动任务#Powershell 自动任务":[119,168],"#自动任务编写#设置自动任务#Powershell 自动任务#{1}":[120,122],"#自动任务编写#设置自动任务#Powershell 自动任务#{2}":[123,123],"#自动任务编写#设置自动任务#Powershell 自动任务#{3}":[124,127],"#自动任务编写#设置自动任务#Powershell 自动任务#{4}":[128,128],"#自动任务编写#设置自动任务#Powershell 自动任务#{5}":[129,129],"#自动任务编写#设置自动任务#Powershell 自动任务#{6}":[130,133],"#自动任务编写#设置自动任务#Powershell 自动任务#{7}":[134,134],"#自动任务编写#设置自动任务#Powershell 自动任务#{8}":[135,135],"#自动任务编写#设置自动任务#Powershell 自动任务#{9}":[136,139],"#自动任务编写#设置自动任务#Powershell 自动任务#{10}":[140,140],"#自动任务编写#设置自动任务#Powershell 自动任务#{11}":[141,141],"#自动任务编写#设置自动任务#Powershell 自动任务#{12}":[142,145],"#自动任务编写#设置自动任务#Powershell 自动任务#{13}":[146,146],"#自动任务编写#设置自动任务#Powershell 自动任务#{14}":[147,147],"#自动任务编写#设置自动任务#Powershell 自动任务#{15}":[148,157],"#自动任务编写#设置自动任务#Powershell 自动任务#{16}":[158,158],"#自动任务编写#设置自动任务#Powershell 自动任务#{17}":[159,163],"#自动任务编写#设置自动任务#Powershell 自动任务#{18}":[164,165],"#自动任务编写#设置自动任务#Powershell 自动任务#{19}":[166,168],"#自动任务编写#任务查看":[169,209],"#自动任务编写#任务查看#{1}":[171,209]},"last_import":{"mtime":1747536239370,"size":6702,"at":1748488128956,"hash":"103kd6v"},"key":"Rational thinking/其他仓库/计算机科学/操作系统-分支/操作系统/Windows系统/软件管理器/Powershell.md","last_embed":{"hash":"103kd6v","at":1750737384474}},