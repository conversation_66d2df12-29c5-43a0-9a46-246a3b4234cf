{"edges": [{"fromNode": "14fbab66d68b45e4", "fromSide": "bottom", "id": "49d8f08298b8d445", "label": "存储程序思想", "styleAttributes": {}, "toNode": "7f5a3b8c47d7ac6e", "toSide": "top"}], "nodes": [{"displayOverride": false, "file": "Rational thinking/人工智能知识点/图灵机.md", "height": 400, "id": "14fbab66d68b45e4", "role": "", "styleAttributes": {}, "type": "file", "width": 480, "x": -100, "y": -220}, {"displayOverride": false, "file": "数据分析/冯·诺依曼架构.md", "height": 400, "id": "7f5a3b8c47d7ac6e", "role": "", "styleAttributes": {}, "type": "file", "width": 500, "x": -110, "y": 600}, {"displayOverride": false, "file": "Rational thinking/人工智能知识点/人工智能.md", "height": 400, "id": "29cb725eceb08cf0", "role": "user", "styleAttributes": {}, "type": "file", "width": 500, "x": 840, "y": 180}], "metadata": {"version": "1.0-1.0", "frontmatter": {}}}