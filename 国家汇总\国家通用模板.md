
---
aliases: # (字符串或字符串列表)
  - 
英文:  # (字符串)
tags:  # (标签列表, 例如 - tag1)
  - 政治体/国家
  - 
文档更新时间:  # (日期格式, 例如 YYYY-MM-DD)
所属大洲:  # (字符串)
GDP(亿美元):  # (纯数值, 不要加入特殊符号)
官网:  # (URL 列表)
  - 
首都:  # (字符串, 例如[[城市名称]])
最大城市:  # (字符串)
占地面积(km²):  # (纯数值)
海岸线(Km):  # (纯数值)
水域率:  # (纯数值)
官方语言:  # (字符串列表)
  - 
宗教信仰:  # (字符串列表，不显示百分比)
  - 
政府:  # (字符串)
政府性质:  # (字符串)
国家体制:  # (字符串列表)
  - 
立法机构:  # (字符串)
货币:  # (字符串列表, 格式: 名称(代码))
  - 
电话区号:  # (字符串, 格式: "+国家代码")
互联网顶级域:  # (字符串)
时区:  # (字符串, 格式: UTC±数值)
全民识字率:  # (纯数值)
民族数量:  # (纯数值)
人口密度(km²):  # (纯数值)
人均GDP(美元):  # (纯数值)
人口(万):  # (纯数值，示例：17000)
人口增长率:  # (纯数值)
基尼系数:  # (纯数值)
人类发展指数:  # (纯数值)
常备军(万):  # (纯数值)
预备役(万):  # (纯数值)
驾驶方位:  # (字符串, 例如: 靠右行驶)
自然资源:  # (字符串列表)
海军(万):  # (纯数值)
陆军(万):  # (纯数值)
空军(万):  # (纯数值)
铁路总长(公里):  # (纯数值，保留整数)
机场(综合):  # (纯数值)
变电站(综合):  # (纯数值)
年均发电(太瓦时):  # (纯数值)
失业率:  # (纯数值)
森林覆盖率:  # (纯数值)
经济实力评分:  # (纯数值)
城镇化率:  # (纯数值)
老龄化率:  # (纯数值)
男性平均身高(cm):  # (纯数值) 可以通过https://www.worlddata.info/average-bodyheight.php 获取数据
女性平均身高(cm):  # (纯数值)
locations:  # (JSON数组或逗号分隔的数值对, 例如 [latitude, longitude] 或 latitude, longitude)
  - 
  - 
cssclasses:  # (字符串列表)
  - editor-full
---



# 简介 
## 概况
> (在此处填写国家概况，例如地理位置、面积、国际地位等)

## 主要特征
> (在此处填写国家的主要特征，例如经济体量、军事实力等)


---

# 地理相关

## 领土构成
(在此处填写领土相关内容 ，例如主要领土组成、海外领土等)

---

## 地理环境
(在此处填写地理环境信息，例如气候、地形、重要河流山脉等)

---

# 政治相关
> (在此处填写国家政治体系概述)

## (主要政党一)
(在此处填写主要政党一的相关信息)

## (主要政党二)
(在此处填写主要政党二的相关信息)

---

# 人口 / 种族

## 人口构成
(在此处填写人口构成信息，例如总人口、年龄结构、人口密度等)

## 民族/种族
(在此处填写主要民族/种族信息)

---

# 历史相关
> (在此处填写国家历史概述或引用历史时间线笔记，例如 [[(国家名称)历史时间线]])

---

# 价值观
> (在此处填写国家核心价值观或社会文化特征，可使用不同类型的Callout)
>[!info] 例如：非宗教型国家

---

# 军事相关
> (在此处填写军事力量、军事思想等相关内容)

---

# 经济相关
## 主要产业
(在此处填写国家的主要产业结构)

## 经济属性
> (在此处填写经济发展特征或重要经济现象)
>[!warning] 例如：行政交界处的地区, 经济往往较为落后

---
