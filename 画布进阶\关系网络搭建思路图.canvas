{"edges": [{"fromNode": "1522d49f62674874", "fromSide": "right", "id": "87048827e8c06d66", "label": "第一步", "styleAttributes": {}, "toNode": "a263d95fabf655da", "toSide": "left"}, {"fromNode": "a263d95fabf655da", "fromSide": "top", "id": "1f3a763e053b66d2", "styleAttributes": {}, "toNode": "b1b012146f486c53", "toSide": "bottom"}, {"fromNode": "a263d95fabf655da", "fromSide": "bottom", "id": "eb36239d266b0323", "styleAttributes": {}, "toNode": "a3fcb9b99e356fe8", "toSide": "top"}], "nodes": [{"displayOverride": false, "height": 270, "id": "a263d95fabf655da", "role": "", "styleAttributes": {}, "text": "# 分词处理\n* 这一步首先将获取到的JSON数据当中的标题进行中文分词处理", "type": "text", "width": 360, "x": 40, "y": -200}, {"displayOverride": false, "height": 340, "id": "1522d49f62674874", "role": "", "styleAttributes": {}, "text": "# 关系网络搭建", "type": "text", "width": 320, "x": -780, "y": -235}, {"displayOverride": false, "height": 400, "id": "a3fcb9b99e356fe8", "role": "", "styleAttributes": {}, "text": "# 分词处理的缺陷", "type": "text", "width": 380, "x": -300, "y": 280}, {"displayOverride": false, "height": 80, "id": "b1b012146f486c53", "role": "", "styleAttributes": {}, "text": "# 需要的工具", "type": "text", "width": 220, "x": -140, "y": -480}], "metadata": {"version": "1.0-1.0", "frontmatter": {}}}